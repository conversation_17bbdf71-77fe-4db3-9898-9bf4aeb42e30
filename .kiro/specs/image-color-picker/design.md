# 设计文档

## 概述

图片取色功能是一个完整的图像颜色提取系统，允许用户通过拍照或从相册选择图片来提取主要颜色。该功能采用 MVVM 架构模式，使用 SwiftUI 和 Swift Concurrency 实现，集成了相机、相册访问和高级颜色提取算法。

## 架构

### 整体架构模式
- **MVVM 架构**: 视图层、视图模型层和模型层分离
- **@Observable**: 使用 iOS 17+ 的 Observation 框架进行状态管理
- **Swift Concurrency**: 使用 async/await 处理异步操作
- **NavigationStack**: 使用现代导航系统

### 模块结构
```
ImageColorPicker/
├── Views/
│   ├── ImageColorPickerView.swift          // 主入口视图
│   ├── ImageSourceBottomSheet.swift        // 底部弹窗选择
│   └── ColorExtractionView.swift           // 颜色提取界面
├── ViewModels/
│   ├── ImageColorPickerViewModel.swift     // 主视图模型
│   └── ColorExtractionViewModel.swift      // 颜色提取视图模型
├── Models/
│   ├── ImageSource.swift                   // 图片来源枚举
│   └── ExtractedColor.swift                // 提取的颜色模型
├── Services/
│   ├── CameraService.swift                 // 相机服务
│   ├── PhotoLibraryService.swift           // 相册服务
│   └── ColorExtractionService.swift        // 颜色提取服务
└── Extensions/
    └── UIImage+ColorExtraction.swift       // UIImage 颜色提取扩展

集成点:
├── HomeView.swift (修改)                   // 添加导航到图片取色功能
└── ToolCard.swift (修改)                   // 添加图片取色的点击处理
```

## 组件和接口

### 1. 主要视图组件

#### ImageColorPickerView
- **职责**: 作为功能主界面，集成到现有的 ToolCard 导航系统中
- **入口点**: 通过 HomeView 中的 "image_color_extractor" ToolCard 触发
- **状态**: 管理弹窗显示状态和导航状态
- **交互**: 响应用户点击，显示图片来源选择

#### ImageSourceBottomSheet
- **职责**: 显示图片来源选择选项（拍照、相册、取消）
- **样式**: 使用 SwiftUI 的 `.sheet()` 或自定义底部弹窗
- **交互**: 处理用户选择并触发相应的服务

#### ColorExtractionView
- **职责**: 显示选中图片和提取的颜色
- **布局**: 上下分区布局，上部显示图片，下部显示颜色网格
- **状态**: 加载状态、错误状态、成功状态

### 2. 视图模型

#### ImageColorPickerViewModel
```swift
@Observable
class ImageColorPickerViewModel {
    var isBottomSheetPresented: Bool = false
    var selectedImage: UIImage?
    var navigationPath = NavigationPath()
    
    func showImageSourceOptions()
    func handleImageSelection(_ image: UIImage)
    func resetSelection()
}
```

#### ColorExtractionViewModel
```swift
@Observable
class ColorExtractionViewModel {
    var image: UIImage?
    var extractedColors: [ExtractedColor] = []
    var isLoading: Bool = false
    var errorMessage: String?
    
    func extractColors() async
    func retryExtraction() async
    func selectNewImage()
}
```

### 3. 服务层

#### CameraService
```swift
class CameraService {
    func requestCameraPermission() async -> Bool
    func presentCamera() -> UIImagePickerController
    func handleCameraResult(_ result: Result<UIImage, Error>)
}
```

#### PhotoLibraryService
```swift
class PhotoLibraryService {
    func requestPhotoLibraryPermission() async -> Bool
    func presentPhotoLibrary() -> PHPickerViewController
    func handlePhotoSelection(_ result: Result<UIImage, Error>)
}
```

#### ColorExtractionService
```swift
class ColorExtractionService {
    func extractDominantColors(
        from image: UIImage,
        count: Int = 8,
        passes: Int = 5,
        perceptual: Bool = true
    ) async throws -> [ExtractedColor]
    
    private func preprocessImage(_ image: UIImage) -> UIImage?
    private func performKMeansExtraction(_ ciImage: CIImage, count: Int, passes: Int, perceptual: Bool) -> CIImage?
    private func parseColorResults(_ cgImage: CGImage) -> [ExtractedColor]
}
```

## 数据模型

### ImageSource
```swift
enum ImageSource: CaseIterable {
    case camera
    case photoLibrary
    case cancel
    
    var title: LocalizedStringKey { }
    var icon: String { }
}
```

### ExtractedColor
```swift
struct ExtractedColor: Identifiable, Hashable {
    let id = UUID()
    let color: Color
    let weight: Double
    let hexString: String
    
    init(uiColor: UIColor)
}
```

### ColorExtractionState
```swift
enum ColorExtractionState {
    case idle
    case loading
    case success([ExtractedColor])
    case error(String)
}
```

## 错误处理

### 权限错误
- **相机权限被拒绝**: 显示设置引导提示
- **相册权限被拒绝**: 显示设置引导提示（iOS 14+ 使用 PHPickerViewController 不需要权限）
- **权限请求失败**: 显示通用错误信息

### 权限配置
- **相机权限**: 需要在 Info.plist 中配置 `NSCameraUsageDescription`
- **相册权限**: iOS 14+ 使用 PHPickerViewController 无需权限配置
- **多语言权限说明**: 在 InfoPlist.xcstrings 中添加权限说明的本地化

### 图片处理错误
- **图片加载失败**: 显示重试选项
- **图片格式不支持**: 显示格式要求提示
- **图片过大**: 自动压缩或提示用户

### 颜色提取错误
- **提取算法失败**: 显示重试选项
- **内存不足**: 降低提取参数重试
- **处理超时**: 显示超时提示

## 测试策略

### 单元测试
- **ColorExtractionService**: 测试颜色提取算法的准确性和性能
- **ViewModels**: 测试状态管理和业务逻辑
- **UIImage+ColorExtraction**: 测试扩展方法的正确性

### 集成测试
- **相机集成**: 测试相机权限和图片获取流程
- **相册集成**: 测试相册权限和图片选择流程
- **导航流程**: 测试完整的用户交互流程

### UI 测试
- **底部弹窗**: 测试弹窗显示和交互
- **颜色提取界面**: 测试布局和响应性
- **错误状态**: 测试各种错误情况的 UI 表现

### 性能测试
- **大图片处理**: 测试大尺寸图片的处理性能
- **内存使用**: 监控内存使用情况
- **提取速度**: 测试不同参数下的提取速度

## 国际化支持

### 字符串本地化
- 所有用户可见文本使用 `LocalizedStringKey`
- 错误消息支持多语言
- 按钮和标签文本本地化

### 布局适配
- 支持从右到左语言的布局
- 文本长度变化的适应性布局
- 不同语言下的字体和间距调整

## 可访问性

### VoiceOver 支持
- 为所有交互元素添加可访问性标签
- 颜色信息的语音描述
- 导航提示和状态反馈

### 动态字体
- 支持系统动态字体大小
- 布局适应字体大小变化
- 保持可读性和可用性

## 性能优化

### 图片处理优化
- 图片压缩和尺寸调整
- 异步处理避免 UI 阻塞
- 内存管理和释放

### 颜色提取优化
- 可配置的提取参数
- 渐进式结果显示
- 取消机制支持

### UI 性能
- 懒加载和视图复用
- 动画性能优化
- 响应式布局优化

## 颜色提取算法设计

### 算法概述
使用 Core Image 的 K-Means 聚类算法提取图片中的主要颜色。该算法通过迭代聚类将相似颜色分组，并返回每个聚类的代表颜色及其权重。

### 算法流程
1. **图片预处理**
   - 图片尺寸优化（避免过大图片影响性能）
   - 转换为 CIImage 格式
   - 验证图片格式和有效性

2. **K-Means 聚类**
   - 使用 CIFilter.kMeans() 滤镜
   - 配置参数：颜色数量(count)、迭代次数(passes)、感知色彩空间(perceptual)
   - 输出 1xN 的 CIImage，每个像素代表一个聚类中心

3. **结果解析**
   - 从输出 CIImage 中提取像素数据
   - 解析 RGBA 值，其中 Alpha 通道表示颜色权重
   - 转换为 ExtractedColor 对象数组

4. **结果排序和过滤**
   - 按权重降序排列颜色
   - 过滤掉权重过低的颜色
   - 生成十六进制颜色字符串

### 算法参数
- **count**: 提取的颜色数量（默认 8）
- **passes**: K-Means 迭代次数（默认 5，更多迭代更准确但更耗时）
- **perceptual**: 是否使用感知色彩空间（默认 true，结果更符合人眼感受）

### 性能优化策略
- **图片压缩**: 大图片自动缩放到合适尺寸
- **异步处理**: 在后台队列执行算法，避免阻塞 UI
- **取消机制**: 支持用户取消长时间运行的提取操作
- **缓存机制**: 对相同图片的提取结果进行缓存
- **渐进式结果**: 可选择显示中间结果提升用户体验

### 错误处理
- **图片格式错误**: 不支持的图片格式
- **内存不足**: 图片过大导致内存问题
- **算法失败**: Core Image 滤镜执行失败
- **超时处理**: 长时间运行的算法超时保护