# 需求文档

## 介绍

图片取色功能允许用户通过拍照或从相册选择图片来提取主要颜色。该功能包括底部弹窗选择界面、相机/相册集成，以及专门的颜色提取和显示界面。

## 需求

### 需求 1

**用户故事:** 作为用户，我希望能够通过点击按钮触发图片取色功能，以便我可以从图片中提取颜色。

#### 验收标准

1. 当用户点击图片取色按钮时，系统应该显示一个底部弹窗（bottom sheet）
2. 底部弹窗应该包含三个选项：从相册、拍照、取消操作
3. 当用户点击取消操作时，系统应该关闭底部弹窗

### 需求 2

**用户故事:** 作为用户，我希望能够通过拍照获取图片，以便我可以从新拍摄的照片中提取颜色。

#### 验收标准

1. 当用户选择拍照选项时，系统应该请求相机权限
2. 如果权限被授予，系统应该打开系统相机界面
3. 当用户完成拍照后，系统应该获取拍照结果
4. 如果拍照成功，系统应该导航到颜色提取界面并传递图片数据

### 需求 3

**用户故事:** 作为用户，我希望能够从相册选择图片，以便我可以从已有的照片中提取颜色。

#### 验收标准

1. 当用户选择从相册选项时，系统应该打开相册选择界面
2. 当用户选择图片后，系统应该获取选中的图片
3. 系统应该导航到颜色提取界面并传递选中的图片数据

### 需求 4

**用户故事:** 作为用户，我希望在颜色提取界面看到选中的图片和提取的颜色，以便我可以查看和使用这些颜色。

#### 验收标准

1. 颜色提取界面应该使用 NavigationStack 显示标题和返回按钮
2. 界面应该分为两个区域：上部分显示用户选中的图片，下部分显示提取的多个颜色
3. 当界面加载时，系统应该显示加载状态（内嵌样式，非弹窗）
4. 系统应该异步执行颜色提取算法，提取指定数量的主要颜色
5. 提取完成后，系统应该在下部分区域显示提取的颜色
6. 颜色应该按权重降序排列显示

### 需求 5

**用户故事:** 作为用户，我希望能够在颜色提取界面重新选择图片，以便我可以更换要分析的图片。

#### 验收标准

1. 颜色提取界面应该包含一个"重新选择图片"按钮
2. 当用户点击重新选择按钮时，系统应该返回到图片选择流程
3. 用户应该能够重新选择拍照或从相册选择

### 需求 6

**用户故事:** 作为用户，我希望应用能够正确处理权限和错误情况，以便我能够顺利使用功能。

#### 验收标准

1. 当相机权限被拒绝时，系统应该显示适当的错误提示
2. 当相册访问权限被拒绝时，系统应该显示适当的错误提示
3. 当图片加载失败时，系统应该显示错误状态
4. 当颜色提取失败时，系统应该显示错误提示

### 需求 7

**用户故事:** 作为用户，我希望界面符合 iOS 设计规范并支持国际化，以便我能够在不同设备和语言环境下使用。

#### 验收标准

1. 所有界面应该使用 SwiftUI 实现
2. 布局应该对不同 iOS 屏幕尺寸具有响应性和适应性
3. 所有字符串应该符合 iOS 国际化标准
4. 应用应该遵循 MVVM 架构模式，使用 @Observable 进行状态管理
5. 导航应该使用 NavigationStack 实现
6. 日志记录应该使用 Logger 系统