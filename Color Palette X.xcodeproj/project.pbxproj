// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		17E159512E269450005F78C8 /* RandomColor in Frameworks */ = {isa = PBXBuildFile; productRef = 17E159502E269450005F78C8 /* RandomColor */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		17E158B02E228E8D005F78C8 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 17E158972E228E8C005F78C8 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 17E1589E2E228E8C005F78C8;
			remoteInfo = "Color Palette X";
		};
		17E158BA2E228E8D005F78C8 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 17E158972E228E8C005F78C8 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 17E1589E2E228E8C005F78C8;
			remoteInfo = "Color Palette X";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		17E1589F2E228E8C005F78C8 /* Color Palette X.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "Color Palette X.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		17E158AF2E228E8D005F78C8 /* Color Palette XTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "Color Palette XTests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		17E158B92E228E8D005F78C8 /* Color Palette XUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "Color Palette XUITests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		17E158A12E228E8C005F78C8 /* Color Palette X */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "Color Palette X";
			sourceTree = "<group>";
		};
		17E158B22E228E8D005F78C8 /* Color Palette XTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "Color Palette XTests";
			sourceTree = "<group>";
		};
		17E158BC2E228E8D005F78C8 /* Color Palette XUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "Color Palette XUITests";
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		17E1589C2E228E8C005F78C8 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				17E159512E269450005F78C8 /* RandomColor in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		17E158AC2E228E8D005F78C8 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		17E158B62E228E8D005F78C8 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		17E158962E228E8C005F78C8 = {
			isa = PBXGroup;
			children = (
				17E158A12E228E8C005F78C8 /* Color Palette X */,
				17E158B22E228E8D005F78C8 /* Color Palette XTests */,
				17E158BC2E228E8D005F78C8 /* Color Palette XUITests */,
				17E158A02E228E8C005F78C8 /* Products */,
			);
			sourceTree = "<group>";
		};
		17E158A02E228E8C005F78C8 /* Products */ = {
			isa = PBXGroup;
			children = (
				17E1589F2E228E8C005F78C8 /* Color Palette X.app */,
				17E158AF2E228E8D005F78C8 /* Color Palette XTests.xctest */,
				17E158B92E228E8D005F78C8 /* Color Palette XUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		17E1589E2E228E8C005F78C8 /* Color Palette X */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 17E158C32E228E8D005F78C8 /* Build configuration list for PBXNativeTarget "Color Palette X" */;
			buildPhases = (
				17E1589B2E228E8C005F78C8 /* Sources */,
				17E1589C2E228E8C005F78C8 /* Frameworks */,
				17E1589D2E228E8C005F78C8 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				17E158A12E228E8C005F78C8 /* Color Palette X */,
			);
			name = "Color Palette X";
			packageProductDependencies = (
				17E159502E269450005F78C8 /* RandomColor */,
			);
			productName = "Color Palette X";
			productReference = 17E1589F2E228E8C005F78C8 /* Color Palette X.app */;
			productType = "com.apple.product-type.application";
		};
		17E158AE2E228E8D005F78C8 /* Color Palette XTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 17E158C62E228E8D005F78C8 /* Build configuration list for PBXNativeTarget "Color Palette XTests" */;
			buildPhases = (
				17E158AB2E228E8D005F78C8 /* Sources */,
				17E158AC2E228E8D005F78C8 /* Frameworks */,
				17E158AD2E228E8D005F78C8 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				17E158B12E228E8D005F78C8 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				17E158B22E228E8D005F78C8 /* Color Palette XTests */,
			);
			name = "Color Palette XTests";
			packageProductDependencies = (
			);
			productName = "Color Palette XTests";
			productReference = 17E158AF2E228E8D005F78C8 /* Color Palette XTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		17E158B82E228E8D005F78C8 /* Color Palette XUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 17E158C92E228E8D005F78C8 /* Build configuration list for PBXNativeTarget "Color Palette XUITests" */;
			buildPhases = (
				17E158B52E228E8D005F78C8 /* Sources */,
				17E158B62E228E8D005F78C8 /* Frameworks */,
				17E158B72E228E8D005F78C8 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				17E158BB2E228E8D005F78C8 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				17E158BC2E228E8D005F78C8 /* Color Palette XUITests */,
			);
			name = "Color Palette XUITests";
			packageProductDependencies = (
			);
			productName = "Color Palette XUITests";
			productReference = 17E158B92E228E8D005F78C8 /* Color Palette XUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		17E158972E228E8C005F78C8 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					17E1589E2E228E8C005F78C8 = {
						CreatedOnToolsVersion = 16.2;
					};
					17E158AE2E228E8D005F78C8 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 17E1589E2E228E8C005F78C8;
					};
					17E158B82E228E8D005F78C8 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 17E1589E2E228E8C005F78C8;
					};
				};
			};
			buildConfigurationList = 17E1589A2E228E8C005F78C8 /* Build configuration list for PBXProject "Color Palette X" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans",
			);
			mainGroup = 17E158962E228E8C005F78C8;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				17E1594F2E269450005F78C8 /* XCRemoteSwiftPackageReference "RandomColorSwift" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 17E158A02E228E8C005F78C8 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				17E1589E2E228E8C005F78C8 /* Color Palette X */,
				17E158AE2E228E8D005F78C8 /* Color Palette XTests */,
				17E158B82E228E8D005F78C8 /* Color Palette XUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		17E1589D2E228E8C005F78C8 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		17E158AD2E228E8D005F78C8 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		17E158B72E228E8D005F78C8 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		17E1589B2E228E8C005F78C8 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		17E158AB2E228E8D005F78C8 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		17E158B52E228E8D005F78C8 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		17E158B12E228E8D005F78C8 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 17E1589E2E228E8C005F78C8 /* Color Palette X */;
			targetProxy = 17E158B02E228E8D005F78C8 /* PBXContainerItemProxy */;
		};
		17E158BB2E228E8D005F78C8 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 17E1589E2E228E8C005F78C8 /* Color Palette X */;
			targetProxy = 17E158BA2E228E8D005F78C8 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		17E158C12E228E8D005F78C8 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_KEY_NSCameraUsageDescription = "For color extraction only";
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		17E158C22E228E8D005F78C8 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_KEY_NSCameraUsageDescription = "For color extraction only";
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_EMIT_LOC_STRINGS = YES;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		17E158C42E228E8D005F78C8 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"Color Palette X/Preview Content\"";
				DEVELOPMENT_TEAM = RMX32XVT8F;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.jack.Color-Palette-X";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		17E158C52E228E8D005F78C8 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"Color Palette X/Preview Content\"";
				DEVELOPMENT_TEAM = RMX32XVT8F;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.jack.Color-Palette-X";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		17E158C72E228E8D005F78C8 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = RMX32XVT8F;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.jack.Color-Palette-XTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Color Palette X.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Color Palette X";
			};
			name = Debug;
		};
		17E158C82E228E8D005F78C8 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = RMX32XVT8F;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.jack.Color-Palette-XTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Color Palette X.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Color Palette X";
			};
			name = Release;
		};
		17E158CA2E228E8D005F78C8 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = RMX32XVT8F;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.jack.Color-Palette-XUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = "Color Palette X";
			};
			name = Debug;
		};
		17E158CB2E228E8D005F78C8 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = RMX32XVT8F;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.jack.Color-Palette-XUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = "Color Palette X";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		17E1589A2E228E8C005F78C8 /* Build configuration list for PBXProject "Color Palette X" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				17E158C12E228E8D005F78C8 /* Debug */,
				17E158C22E228E8D005F78C8 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		17E158C32E228E8D005F78C8 /* Build configuration list for PBXNativeTarget "Color Palette X" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				17E158C42E228E8D005F78C8 /* Debug */,
				17E158C52E228E8D005F78C8 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		17E158C62E228E8D005F78C8 /* Build configuration list for PBXNativeTarget "Color Palette XTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				17E158C72E228E8D005F78C8 /* Debug */,
				17E158C82E228E8D005F78C8 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		17E158C92E228E8D005F78C8 /* Build configuration list for PBXNativeTarget "Color Palette XUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				17E158CA2E228E8D005F78C8 /* Debug */,
				17E158CB2E228E8D005F78C8 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		17E1594F2E269450005F78C8 /* XCRemoteSwiftPackageReference "RandomColorSwift" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/onevcat/RandomColorSwift.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.0.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		17E159502E269450005F78C8 /* RandomColor */ = {
			isa = XCSwiftPackageProductDependency;
			package = 17E1594F2E269450005F78C8 /* XCRemoteSwiftPackageReference "RandomColorSwift" */;
			productName = RandomColor;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 17E158972E228E8C005F78C8 /* Project object */;
}
