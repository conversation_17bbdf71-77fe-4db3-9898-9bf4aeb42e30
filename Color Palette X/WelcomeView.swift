//
//  WelcomeView.swift
//  Color Palette X
//
//  Created by <PERSON><PERSON><PERSON> on 2025/7/15.
//

import SwiftUI
import RandomColor

/// 随机颜色网格展示界面
///
/// 主要功能：
/// - 显示网格布局的随机颜色
/// - 自动无限垂直滚动
/// - 触摸暂停滚动功能
/// - 重新生成颜色按钮
/// - 每个颜色块支持复制功能
struct WelcomeView: View {

    // MARK: - ViewModel
    
    @State private var viewModel = WelcomeViewModel()
    
    // MARK: - UI 状态
    
    /// ScrollPosition 控制器
    @State private var scrollPosition = ScrollPosition()
    


    // MARK: - 初始化

    init() {
        AppLogger.info("WelcomeView 初始化 - 随机颜色网格展示界面", category: .ui)
    }

    // MARK: - 视图主体

    var body: some View {
        NavigationStack {
            ZStack {
                // 背景
                SwiftUIColor(.systemGroupedBackground)
                    .ignoresSafeArea()

                VStack(spacing: 0) {
                    // 主滚动内容区域
                    scrollContentView

                    // 底部控制区域
                    bottomControlView
                }
            }
            .navigationTitle("color_palette_title")
            .navigationBarTitleDisplayMode(.inline)
            .onAppear {
                setupInitialData()
            }
            .onDisappear {
                cleanupResources()
            }
            .onChange(of: viewModel.isAutoScrollEnabled) { _, newValue in
                if newValue {
                    viewModel.startAutoScroll()
                } else {
                    viewModel.stopAutoScroll()
                }
            }
        }
    }

    // MARK: - 子视图组件
    
    /// 响应式网格列配置
    private var gridColumns: [GridItem] {
        let screenWidth = UIScreen.main.bounds.width
        let padding: CGFloat = 32 // 左右边距
        let spacing: CGFloat = 16 // 网格间距
        let minSquareSize: CGFloat = 80
        
        let availableWidth = screenWidth - padding
        let columnsCount = max(2, Int(availableWidth / (minSquareSize + spacing)))
        
        return Array(repeating: GridItem(.flexible(), spacing: spacing), count: columnsCount)
    }
    
    /// 计算方块大小
    private var squareSize: CGFloat {
        let screenWidth = UIScreen.main.bounds.width
        let padding: CGFloat = 32
        let spacing: CGFloat = 16
        let columnsCount = gridColumns.count
        
        let availableWidth = screenWidth - padding - (CGFloat(columnsCount - 1) * spacing)
        return availableWidth / CGFloat(columnsCount)
    }

    var colorsScrollView: some View {
        GeometryReader { geometryProxy in
            ScrollView(.vertical, showsIndicators: false) {
                LazyVGrid(columns: gridColumns, spacing: 16) {
                    ForEach(Array(viewModel.colors.enumerated()), id: \.offset) { index, color in
                        ColorSquareView(
                            color: color,
                            showHexValue: true, size: squareSize
                        )
                        .id("color_\(index)")
                        .transition(.asymmetric(
                            insertion: .move(edge: .bottom).combined(with: .opacity),
                            removal: .move(edge: .top).combined(with: .opacity)
                        ))
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
            }
            .scrollPosition($scrollPosition)
            .onScrollGeometryChange(for: ScrollGeometry.self) { geometry in
                return geometry
            } action: { _, newGeometry in
                viewModel.scrollGeometry = newGeometry
                AppLogger.debug("ScrollGeometry 更新: contentOffset.y = \(newGeometry.contentOffset.y)", category: .ui)
            }
            .simultaneousGesture(
                // 触摸手势检测 - 手指按下暂停滚动
                DragGesture(minimumDistance: 0)
                    .onChanged { _ in
                        viewModel.handleTouchBegan()
                    }
                    .onEnded { _ in
                        viewModel.handleTouchEnded()
                    }
            )
            .onChange(of: geometryProxy.safeAreaInsets.top) { _, newSafeArea in
                viewModel.safeArea = newSafeArea
            }
        }
    }
    /// 滚动内容视图
    private var scrollContentView: some View {
        TimelineView(.animation) { context in
            if let newScrollY = viewModel.updateScrollPosition(at: context.date) {
                scrollPosition.scrollTo(y: newScrollY)
            }
            return colorsScrollView
        }
    }

    /// 底部控制视图
    private var bottomControlView: some View {
        VStack(spacing: 12) {
            // 状态指示器
            HStack(spacing: 16) {
                // 自动滚动状态
                HStack(spacing: 6) {
                    Circle()
                        .fill(viewModel.isAutoScrolling ? .green : (viewModel.isAutoScrollEnabled ? .orange : .gray))
                        .frame(width: 8, height: 8)
                        .animation(.easeInOut(duration: 0.3), value: viewModel.isAutoScrolling)
                        .animation(.easeInOut(duration: 0.3), value: viewModel.isAutoScrollEnabled)

                    Text(viewModel.getScrollStatusText())
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.secondary)
                }

                Spacer()

                // 颜色数量
                Text("colors_count \(viewModel.colors.count)")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.secondary)
            }

            // 控制按钮区域 - 响应式布局
            HStack(spacing: 12) {
                // 自动滚动控制按钮
                Button(action: {
                    toggleAutoScroll()
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: viewModel.isAutoScrollEnabled ? "pause.fill" : "play.fill")
                            .font(.system(size: 16, weight: .medium))
                            .animation(.spring(response: 0.3, dampingFraction: 0.6), value: viewModel.isAutoScrollEnabled)
                        
                        Text(viewModel.isAutoScrollEnabled ? "auto_scroll_pause" : "auto_scroll_resume")
                            .font(.system(size: 16, weight: .semibold))
                            .lineLimit(1)
                            .minimumScaleFactor(0.8)
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 50)
                    .background(
                        LinearGradient(
                            colors: viewModel.isAutoScrollEnabled ? [.orange, .red] : [.green, .blue],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                        .animation(.easeInOut(duration: 0.3), value: viewModel.isAutoScrollEnabled)
                    )
                    .clipShape(RoundedRectangle(cornerRadius: 25))
                    .shadow(
                        color: (viewModel.isAutoScrollEnabled ? SwiftUIColor.orange : SwiftUIColor.green).opacity(0.3),
                        radius: 8,
                        x: 0,
                        y: 4
                    )
                }
                .buttonStyle(PlainButtonStyle())
                .scaleEffect(viewModel.isAutoScrollEnabled ? 1.0 : 0.98)
                .animation(.spring(response: 0.4, dampingFraction: 0.7), value: viewModel.isAutoScrollEnabled)

                // 重新生成按钮
                Button(action: {
                    regenerateColors()
                }) {
                    HStack(spacing: 8) {
                        if viewModel.isGenerating {
                            ProgressView()
                                .scaleEffect(0.8)
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        } else {
                            Image(systemName: "arrow.clockwise")
                                .font(.system(size: 16, weight: .medium))
                        }

                        Text(viewModel.isGenerating ? "generating" : "regenerate_colors")
                            .font(.system(size: 16, weight: .semibold))
                            .lineLimit(1)
                            .minimumScaleFactor(0.8)
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 50)
                    .background(
                        LinearGradient(
                            colors: [.blue, .purple],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .clipShape(RoundedRectangle(cornerRadius: 25))
                    .shadow(
                        color: .blue.opacity(0.3),
                        radius: 8,
                        x: 0,
                        y: 4
                    )
                }
                .buttonStyle(PlainButtonStyle())
                .disabled(viewModel.isGenerating)
                .scaleEffect(viewModel.isGenerating ? 0.95 : 1.0)
                .animation(.spring(response: 0.3, dampingFraction: 0.6), value: viewModel.isGenerating)
            }
            .padding(.horizontal, 4)
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .background(
            .ultraThinMaterial,
            in: RoundedRectangle(cornerRadius: 0)
        )
    }

    // MARK: - 数据管理方法

    /// 设置初始数据
    private func setupInitialData() {
        AppLogger.info("开始设置初始颜色数据", category: .ui)
        


        Task {
            await viewModel.generateRandomColors()
            
            AppLogger.info("初始数据设置完成，共 \(viewModel.colors.count) 个颜色", category: .ui)
            
            // 延迟启动自动滚动，确保视图已完全加载
            try? await Task.sleep(nanoseconds: 600_000_000) // 0.6秒
            viewModel.startAutoScroll()
        }
    }

    /// 重新生成颜色
    private func regenerateColors() {
        guard !viewModel.isGenerating else { return }

        AppLogger.info("开始重新生成颜色", category: .ui)

        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()

        Task {
            // 暂停自动滚动
            viewModel.stopAutoScroll()
            
            // 重置滚动位置
            viewModel.currentScrollY = 0
            scrollPosition.scrollTo(y: 0)
            
            // 生成新颜色
            await viewModel.generateRandomColors()
            
            AppLogger.info("颜色重新生成完成，共 \(viewModel.colors.count) 个颜色", category: .ui)
            
            // 延迟恢复自动滚动
            try? await Task.sleep(nanoseconds: 1_000_000_000) // 1.0秒
            if viewModel.isAutoScrollEnabled {
                viewModel.startAutoScroll()
            }
        }
    }

    // MARK: - 自动滚动控制方法

    /// 切换自动滚动状态
    private func toggleAutoScroll() {
        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
        
        // 更新 AppStorage 和 ViewModel
        isAutoScrollEnabledStorage.toggle()
        viewModel.toggleAutoScroll()
    }

    // MARK: - 资源管理方法

    /// 清理资源，防止内存泄漏
    private func cleanupResources() {
        AppLogger.info("开始清理 WelcomeView 资源", category: .ui)

        // 停止自动滚动
        viewModel.stopAutoScroll()

        AppLogger.info("WelcomeView 资源清理完成", category: .ui)
    }
}

// MARK: - 功能特色卡片（保留原有组件）
struct FeatureCard: View {
    let icon: String
    let title: LocalizedStringKey
    let description: LocalizedStringKey
    let color: SwiftUIColor

    var body: some View {
        VStack(spacing: 12) {
            // 图标
            ZStack {
                Circle()
                    .fill(color.opacity(0.15))
                    .frame(width: 40, height: 40)

                Image(systemName: icon)
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(color)
            }

            VStack(spacing: 4) {
                Text(title)
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(.primary)
                    .multilineTextAlignment(.center)

                Text(description)
                    .font(.system(size: 12))
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(3)
            }
        }
        .frame(maxWidth: .infinity)
        .frame(height: 120)
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(SwiftUIColor(.systemBackground))
                .shadow(
                    color: color.opacity(0.1),
                    radius: 8,
                    x: 0,
                    y: 4
                )
        )
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(color.opacity(0.2), lineWidth: 1)
        )
    }
}

// MARK: - 预览

#Preview {
    WelcomeView()
}


