//
//  TestColorGeneration.swift
//  Color Palette X
//
//  Created by JackFan on 2025/7/15.
//

import SwiftUI
import RandomColor

/// 测试颜色生成功能的简单验证
struct TestColorGeneration {
    
    /// 测试颜色工具类的所有方法
    static func testColorUtils() {
        print("🎨 开始测试颜色工具类...")
        
        // 测试基准色
        let baseColor = SwiftUIColor.blue
        print("基准色: \(ColorUtils.hexString(from: baseColor))")
        
        // 测试深浅色判断
        let isDark = ColorUtils.isDarkColor(baseColor)
        print("是否为深色: \(isDark)")
        
        print("✅ 颜色工具类测试完成")
    }
    
    /// 测试随机颜色生成
    static func testRandomColorGeneration() {
        print("🎨 开始测试随机颜色生成...")
        
        // 测试生成多个随机颜色
        var colors: [SwiftUIColor] = []
        for i in 0..<10 {
            let randomUIColor = randomColor()
            let swiftUIColor = SwiftUIColor(randomUIColor)
            colors.append(swiftUIColor)
            print("颜色 \(i + 1): \(ColorUtils.hexString(from: swiftUIColor))")
        }
        
        print("✅ 随机颜色生成测试完成，共生成 \(colors.count) 个颜色")
    }
    
    /// 测试 RandomColorSwift 集成
    static func testRandomColorIntegration() {
        print("🎨 开始测试 RandomColorSwift 集成...")
        
        // 测试不同的色相和亮度
        let hues: [Hue] = [.red, .blue, .green, .random]
        let luminosities: [Luminosity] = [.bright, .dark, .random]
        
        for hue in hues {
            for luminosity in luminosities {
                let randomUIColor = randomColor(hue: hue, luminosity: luminosity)
                let swiftUIColor = SwiftUIColor(randomUIColor)
                print("色相: \(hue), 亮度: \(luminosity) -> \(ColorUtils.hexString(from: swiftUIColor))")
            }
        }
        
        print("✅ RandomColorSwift 集成测试完成")
    }
    
    /// 运行所有测试
    static func runAllTests() {
        print("🚀 开始运行所有颜色生成测试...")
        print("=" * 50)
        
        testColorUtils()
        print("")
        
        testRandomColorGeneration()
        print("")
        
        testRandomColorIntegration()
        print("")
        
        print("=" * 50)
        print("🎉 所有测试完成！")
    }
}

// MARK: - 字符串扩展（用于重复字符）
extension String {
    static func * (left: String, right: Int) -> String {
        return String(repeating: left, count: right)
    }
}

// MARK: - 预览
#if DEBUG
#Preview("Color Generation Test") {
    VStack(spacing: 20) {
        Text("颜色生成功能测试")
            .font(.title)
            .fontWeight(.bold)
        
        Button("运行所有测试") {
            TestColorGeneration.runAllTests()
        }
        .buttonStyle(.borderedProminent)
        
        Text("查看 Xcode 控制台输出")
            .font(.caption)
            .foregroundColor(.secondary)
        
        Spacer()
    }
    .padding()
}
#endif
