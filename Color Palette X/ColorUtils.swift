//
//  ColorUtils.swift
//  Color Palette X
//
//  Created by <PERSON><PERSON><PERSON> on 2025/7/15.
//

import SwiftUI
import UIKit

/// 颜色工具类
/// 
/// 提供基本的颜色转换和判断功能，包括：
/// - 十六进制颜色字符串转换
/// - 深浅色判断
public final class ColorUtils {
    
    // MARK: - 私有初始化
    
    /// 防止实例化，所有方法都是静态方法
    private init() {}
    
    // MARK: - 颜色转换辅助方法
    
    /// 将 SwiftUI Color 转换为 UIColor
    /// - Parameter color: SwiftUI Color 对象
    /// - Returns: 对应的 UIColor 对象
    private static func uiColor(from color: SwiftUIColor) -> UIColor {
        return UIColor(color)
    }

    /// 从 UIColor 提取 HSB 值
    /// - Parameter color: UIColor 对象
    /// - Returns: 包含色相、饱和度、亮度、透明度的元组
    private static func extractHSB(from color: UIColor) -> (hue: CGFloat, saturation: CGFloat, brightness: CGFloat, alpha: CGFloat) {
        var hue: CGFloat = 0
        var saturation: CGFloat = 0
        var brightness: CGFloat = 0
        var alpha: CGFloat = 0
        
        color.getHue(&hue, saturation: &saturation, brightness: &brightness, alpha: &alpha)
        
        return (hue: hue, saturation: saturation, brightness: brightness, alpha: alpha)
    }
    
    // MARK: - 公共工具方法
    
    /// 获取颜色的十六进制字符串表示
    /// - Parameter color: SwiftUI Color 对象
    /// - Returns: 十六进制颜色字符串（如 "#FF5733"）
    public static func hexString(from color: SwiftUIColor) -> String {
        let uiColor = uiColor(from: color)
        
        var red: CGFloat = 0
        var green: CGFloat = 0
        var blue: CGFloat = 0
        var alpha: CGFloat = 0
        
        uiColor.getRed(&red, green: &green, blue: &blue, alpha: &alpha)
        
        let r = Int(red * 255)
        let g = Int(green * 255)
        let b = Int(blue * 255)
        
        return String(format: "#%02X%02X%02X", r, g, b)
    }
    
    /// 判断颜色是否为深色
    /// - Parameter color: SwiftUI Color 对象
    /// - Returns: 如果是深色返回 true，否则返回 false
    public static func isDarkColor(_ color: SwiftUIColor) -> Bool {
        let uiColor = uiColor(from: color)
        let hsb = extractHSB(from: uiColor)
        
        // 亮度小于0.5认为是深色
        return hsb.brightness < 0.5
    }
}

// MARK: - 预览和测试

#if DEBUG
/// ColorUtils 预览和测试
struct ColorUtilsPreview {
    
    /// 测试颜色工具方法
    static func testColorUtils() {
        let testColors = [SwiftUIColor.red, SwiftUIColor.blue, SwiftUIColor.green, SwiftUIColor.purple]
        
        AppLogger.info("开始测试颜色工具功能", category: .general)
        
        for color in testColors {
            let hexString = ColorUtils.hexString(from: color)
            let isDark = ColorUtils.isDarkColor(color)
            
            AppLogger.info("颜色: \(hexString), 是否为深色: \(isDark)", category: .general)
        }
    }
}

#Preview("ColorUtils Test") {
    VStack(spacing: 20) {
        Text("颜色工具类测试")
            .font(.title)
            .fontWeight(.bold)
        
        Button("测试颜色工具") {
            ColorUtilsPreview.testColorUtils()
        }
        .buttonStyle(.borderedProminent)
        
        Text("查看 Xcode 控制台输出")
            .font(.caption)
            .foregroundColor(.secondary)
    }
    .padding()
}
#endif
