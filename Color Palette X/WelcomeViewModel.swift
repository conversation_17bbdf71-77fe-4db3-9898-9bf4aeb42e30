//
//  WelcomeViewModel.swift
//  Color Palette X
//
//  Created by <PERSON><PERSON><PERSON> on 2025/7/26.
//

import SwiftUI
import RandomColor

/// WelcomeView 的视图模型
/// 
/// 负责管理颜色生成、自动滚动等业务逻辑和状态管理
/// 使用 MVVM 架构模式，将业务逻辑从视图中分离
@Observable
final class WelcomeViewModel {
    
    // MARK: - 常量定义
    
    /// UserDefaults 键名常量
    private static let isAutoScrollEnabledKey = "isAutoScrollEnabled"
    
    // MARK: - 颜色数据
    
    /// 当前显示的颜色数组
    var colors: [SwiftUIColor] = []
    
    // MARK: - 自动滚动状态
    
    /// 自动滚动功能是否启用（用户控制的开关，持久化存储）
    var isAutoScrollEnabled: Bool = true {
        didSet {
            UserDefaults.standard.set(isAutoScrollEnabled, forKey: Self.isAutoScrollEnabledKey)
        }
    }
    
    /// 是否正在自动滚动
    var isAutoScrolling = false
    
    /// 当前滚动Y位置
    var currentScrollY: CGFloat = 0
    
    /// 滚动几何信息
    var scrollGeometry: ScrollGeometry?
    
    /// 自动滚动开始时间
    var startTime: Date = Date()
    
    // MARK: - UI 状态
    
    /// 是否正在生成新颜色
    var isGenerating = false
    
    /// 触摸检测状态
    var isTouching = false
    
    /// 安全区域高度
    var safeArea: CGFloat = 0
    
    // MARK: - 配置常量
    
    /// 颜色总数量
    private let colorCount = 100
    
    /// 滚动速度（像素/秒）
    private let scrollSpeed: CGFloat = 50.0
    
    /// 色相选项，用于增加颜色多样性
    private let hueOptions: [Hue] = [.red, .orange, .yellow, .green, .blue, .purple, .pink, .monochrome, .random]
    
    /// 亮度选项，用于增加颜色多样性
    private let luminosityOptions: [Luminosity] = [.bright, .light, .dark, .random]
    
    // MARK: - 初始化
    
    init() {
        AppLogger.info("WelcomeViewModel 初始化", category: .general)
        
        // 从 UserDefaults 读取 isAutoScrollEnabled 的值，如果不存在则使用默认值 true
        if let storedValue = UserDefaults.standard.object(forKey: "isAutoScrollEnabled") as? Bool {
            isAutoScrollEnabled = storedValue
        } else {
            isAutoScrollEnabled = true // 这会触发 didSet，将默认值保存到 UserDefaults
        }
    }
    
    // MARK: - 异步颜色生成
    
    /// 异步生成指定数量的随机颜色
    /// - Parameter count: 要生成的颜色数量，默认使用配置的颜色总数
    @MainActor
    func generateRandomColors(count: Int? = nil) async {
        let targetCount = count ?? colorCount
        
        guard targetCount > 0 else {
            AppLogger.warning("请求生成的颜色数量无效: \(targetCount)", category: .general)
            return
        }
        
        guard targetCount <= 1000 else {
            AppLogger.warning("请求生成的颜色数量过大: \(targetCount)，限制为1000", category: .general)
            await generateRandomColors(count: 1000)
            return
        }
        
        AppLogger.info("开始异步生成 \(targetCount) 个随机颜色", category: .general)
        
        isGenerating = true
        defer { isGenerating = false }
        
        // 使用 TaskGroup 并发生成颜色以提高性能
        let newColors = await withTaskGroup(of: (Int, SwiftUIColor).self, returning: [SwiftUIColor].self) { group in
            
            // 分批处理以避免内存峰值
            let batchSize = min(50, targetCount)
            let batches = (targetCount + batchSize - 1) / batchSize
            
            for batchIndex in 0..<batches {
                let startIndex = batchIndex * batchSize
                let endIndex = min(startIndex + batchSize, targetCount)
                
                // 为每个批次添加任务
                for i in startIndex..<endIndex {
                    group.addTask { [weak self] in
                        guard let self = self else { 
                            return (i, SwiftUIColor.gray) 
                        }
                        
                        // 使用不同的色相和亮度组合增加多样性
                        let hue = self.getHueForIndex(i)
                        let luminosity = self.getLuminosityForIndex(i)
                        
                        // 在后台队列执行耗时的颜色生成
                        let randomUIColor = randomColor(hue: hue, luminosity: luminosity)
                        let swiftUIColor = SwiftUIColor(randomUIColor)
                        
                        return (i, swiftUIColor)
                    }
                }
            }
            
            // 收集所有结果并按索引排序
            var results: [(Int, SwiftUIColor)] = []
            for await result in group {
                results.append(result)
            }
            
            // 按索引排序确保颜色顺序正确
            results.sort { $0.0 < $1.0 }
            return results.map { $0.1 }
        }
        
        // 更新颜色数组
        colors = newColors
        
        AppLogger.info("异步颜色生成完成，共生成 \(colors.count) 个颜色", category: .general)
    }
    
    /// 生成备用颜色数组（当异步生成失败时使用）
    /// - Parameter count: 颜色数量
    /// - Returns: 备用颜色数组
    private func generateFallbackColors(count: Int) -> [SwiftUIColor] {
        AppLogger.info("生成备用颜色数组，数量: \(count)", category: .general)
        
        return (0..<count).map { index in
            let hue = getHueForIndex(index)
            let luminosity = getLuminosityForIndex(index)
            let randomUIColor = randomColor(hue: hue, luminosity: luminosity)
            return SwiftUIColor(randomUIColor)
        }
    }
    
    // MARK: - 辅助方法
    
    /// 根据索引获取色相选项
    /// - Parameter index: 颜色索引
    /// - Returns: 对应的色相选项
    private func getHueForIndex(_ index: Int) -> Hue {
        return hueOptions[index % hueOptions.count]
    }
    
    /// 根据索引获取亮度选项
    /// - Parameter index: 颜色索引
    /// - Returns: 对应的亮度选项
    private func getLuminosityForIndex(_ index: Int) -> Luminosity {
        return luminosityOptions[index % luminosityOptions.count]
    }
    
    // MARK: - 自动滚动控制
    
    /// 开始自动滚动
    func startAutoScroll() {
        guard !isTouching && !colors.isEmpty && isAutoScrollEnabled else { 
            AppLogger.debug("自动滚动启动条件不满足", category: .ui)
            return 
        }
        
        isAutoScrolling = true
        currentScrollY = scrollGeometry?.contentOffset.y ?? currentScrollY
        startTime = Date()
        
        AppLogger.info("开始自动滚动，当前位置: \(currentScrollY)", category: .ui)
    }
    
    /// 停止自动滚动
    func stopAutoScroll() {
        isAutoScrolling = false
        AppLogger.info("停止自动滚动", category: .ui)
    }
    
    /// 切换自动滚动状态
    func toggleAutoScroll() {
        AppLogger.info("切换自动滚动状态：\(isAutoScrollEnabled) -> \(!isAutoScrollEnabled)", category: .ui)
        
        isAutoScrollEnabled.toggle()
        
        if isAutoScrollEnabled {
            AppLogger.info("启用自动滚动", category: .ui)
            startAutoScroll()
        } else {
            AppLogger.info("禁用自动滚动", category: .ui)
            stopAutoScroll()
        }
    }
    
    /// 更新滚动位置 - 基于时间的每帧平滑滚动
    /// - Parameter date: 当前时间
    /// - Returns: 新的滚动位置，如果不需要更新则返回 nil
    func updateScrollPosition(at date: Date) -> CGFloat? {
        // 检查基本条件
        guard !isTouching else { return nil }
        guard !colors.isEmpty else { return nil }
        guard isAutoScrolling && isAutoScrollEnabled else { return nil }
        
        guard let scrollGeometry = scrollGeometry else { return nil }
        
        let scrollViewHeight = scrollGeometry.contentSize.height
        guard scrollViewHeight > 0.0 else { return nil }
        
        // 计算从开始滚动到现在的时间差
        let timeElapsed = date.timeIntervalSince(startTime) * 1000
        startTime = date
        
        guard timeElapsed > 0 else { return nil }
        
        // 基于时间计算滚动距离，确保恒定速度
        let targetScrollY = currentScrollY + 1
        
        // 计算最大滚动距离（内容高度 - 可见高度）
        let maxScrollY = scrollViewHeight - scrollGeometry.visibleRect.height
        
        // 边界检查：滚动到底部时的处理
        if targetScrollY >= maxScrollY {
            if isAutoScrollEnabled {
                AppLogger.info("🔄 自动滚动模式：滚动到底部，需要重新生成颜色", category: .ui)
                Task {
                    await regenerateColorsAndRestart()
                }
            } else {
                AppLogger.info("🛑 自动滚动已禁用：到达底部", category: .ui)
                stopAutoScroll()
            }
            return nil
        }
        
        // 更新滚动位置
        currentScrollY = targetScrollY
        return currentScrollY + safeArea
    }
    
    /// 重新生成颜色并重新开始滚动
    @MainActor
    private func regenerateColorsAndRestart() async {
        // 暂停自动滚动
        stopAutoScroll()
        
        // 重置滚动位置
        currentScrollY = 0
        
        // 生成新的颜色
        await generateRandomColors()
        
        AppLogger.info("重新生成了 \(colors.count) 个新颜色", category: .ui)
        
        // 延迟恢复自动滚动
        try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
        
        if isAutoScrollEnabled {
            AppLogger.info("自动滚动启用，恢复滚动", category: .ui)
            startAutoScroll()
        } else {
            AppLogger.info("自动滚动已禁用，保持静止状态", category: .ui)
        }
    }
    
    /// 处理用户触摸开始
    func handleTouchBegan() {
        isTouching = true
        AppLogger.debug("用户开始触摸，暂停自动滚动", category: .ui)
    }
    
    /// 处理用户触摸结束
    func handleTouchEnded() {
        isTouching = false
        AppLogger.debug("用户结束触摸，自动滚动启用状态: \(isAutoScrollEnabled)", category: .ui)
        
        // 只有在自动滚动启用时才恢复滚动
        if isAutoScrollEnabled {
            startAutoScroll()
        }
    }
    
    /// 获取滚动状态文本
    func getScrollStatusText() -> LocalizedStringKey {
        if isAutoScrolling {
            return "auto_scroll_active"
        } else if isAutoScrollEnabled {
            return "auto_scroll_paused"
        } else {
            return "auto_scroll_disabled"
        }
    }
}

#if DEBUG
#Preview("WelcomeViewModel Test") {
    VStack(spacing: 20) {
        Text("WelcomeViewModel 测试")
            .font(.title)
            .fontWeight(.bold)
        
        Text("ViewModel 已创建")
            .font(.body)
            .foregroundColor(.secondary)
    }
    .padding()
}
#endif