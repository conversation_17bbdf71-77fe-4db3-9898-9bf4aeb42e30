//
//  Logger.swift
//  Color Palette X
//
//  Created by Jack<PERSON><PERSON> on 2025/7/12.
//

import Foundation
import os

/// 日志级别枚举
public enum LogLevel: Int, CaseIterable {
    case debug = 0
    case info = 1
    case warning = 2
    case error = 3
    
    /// 日志级别的字符串表示
    var displayName: String {
        switch self {
        case .debug: return "🐛 DEBUG"
        case .info: return "ℹ️ INFO"
        case .warning: return "⚠️ WARNING"
        case .error: return "❌ ERROR"
        }
    }
    

}

/// 日志模块分类
public enum LogCategory: String, CaseIterable {
    case ui = "UI"
    case network = "Network"
    case data = "Data"
    case auth = "Auth"
    case general = "General"
    case performance = "Performance"
    
    /// 模块的 emoji 标识
    var emoji: String {
        switch self {
        case .ui: return "🎨"
        case .network: return "🌐"
        case .data: return "💾"
        case .auth: return "🔐"
        case .general: return "📝"
        case .performance: return "⚡"
        }
    }
}

/// 统一日志工具类
/// 
/// 提供统一的日志记录功能，支持多种日志级别和模块分类
/// 使用 iOS 14+ 的现代 Logger API，提供更好的性能和字符串插值支持
/// 在 Debug 模式下输出到控制台，Release 模式下可配置输出行为
///
/// 特性：
/// - 使用现代 Logger API 替代旧的 OSLog
/// - 支持字符串插值和隐私控制
/// - 线程安全的异步日志记录
/// - 可配置的日志级别和格式选项
///
/// 使用示例：
/// ```swift
/// AppLogger.debug("用户点击了按钮", category: .ui)
/// AppLogger.info("网络请求开始", category: .network)
/// AppLogger.warning("缓存即将过期", category: .data)
/// AppLogger.error("登录失败", category: .auth)
/// ```
public final class AppLogger {
    
    // MARK: - 单例
    
    /// 共享实例
    public static let shared = AppLogger()
    
    // MARK: - 配置属性
    
    /// 是否启用日志输出
    public var isEnabled: Bool = true
    
    /// 最小日志级别，低于此级别的日志将被忽略
    public var minimumLogLevel: LogLevel = .debug
    
    /// 是否在 Release 模式下禁用日志
    public var disableInRelease: Bool = true
    
    /// 是否显示文件名和行号
    public var showFileInfo: Bool = true
    
    /// 是否显示时间戳
    public var showTimestamp: Bool = true
    
    // MARK: - 私有属性
    
    /// 日期格式化器
    private let dateFormatter: DateFormatter
    
    /// 队列锁，确保线程安全
    private let logQueue = DispatchQueue(label: "com.colorpalettex.logger", qos: .utility)
    
    /// Logger 实例
    private let logger = Logger(subsystem: "com.jack.Color-Palette-X", category: "AppLogger")
    
    // MARK: - 初始化
    
    private init() {
        dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss.SSS"
        dateFormatter.locale = Locale(identifier: "en_US_POSIX")
        
        // 在 Release 模式下默认设置更高的日志级别
        #if DEBUG
        minimumLogLevel = .debug
        #else
        minimumLogLevel = .info
        #endif
    }
    
    // MARK: - 公共方法
    
    /// 记录日志
    /// - Parameters:
    ///   - level: 日志级别
    ///   - message: 日志消息
    ///   - category: 日志分类
    ///   - file: 文件名
    ///   - function: 函数名
    ///   - line: 行号
    public func log(
        level: LogLevel,
        message: @autoclosure () -> String,
        category: LogCategory = .general,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        // 检查是否应该输出日志
        guard shouldLog(level: level) else { return }

        // 立即计算消息以避免 escaping closure 问题
        let messageString = message()

        logQueue.async { [weak self] in
            self?.performLog(
                level: level,
                message: messageString,
                category: category,
                file: file,
                function: function,
                line: line
            )
        }
    }
    
    // MARK: - 便捷方法
    
    /// Debug 级别日志
    public static func debug(
        _ message: @autoclosure () -> String,
        category: LogCategory = .general,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        shared.log(level: .debug, message: message(), category: category, file: file, function: function, line: line)
    }
    
    /// Info 级别日志
    public static func info(
        _ message: @autoclosure () -> String,
        category: LogCategory = .general,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        shared.log(level: .info, message: message(), category: category, file: file, function: function, line: line)
    }
    
    /// Warning 级别日志
    public static func warning(
        _ message: @autoclosure () -> String,
        category: LogCategory = .general,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        shared.log(level: .warning, message: message(), category: category, file: file, function: function, line: line)
    }
    
    /// Error 级别日志
    public static func error(
        _ message: @autoclosure () -> String,
        category: LogCategory = .general,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        shared.log(level: .error, message: message(), category: category, file: file, function: function, line: line)
    }
}

// MARK: - 私有方法扩展

private extension AppLogger {
    
    /// 检查是否应该输出日志
    func shouldLog(level: LogLevel) -> Bool {
        guard isEnabled else { return false }
        
        #if !DEBUG
        if disableInRelease { return false }
        #endif
        
        return level.rawValue >= minimumLogLevel.rawValue
    }
    
    /// 执行日志输出
    func performLog(
        level: LogLevel,
        message: String,
        category: LogCategory,
        file: String,
        function: String,
        line: Int
    ) {
        let formattedMessage = formatMessage(
            level: level,
            message: message,
            category: category,
            file: file,
            function: function,
            line: line
        )
        
        // 输出到控制台
        print(formattedMessage)
        
        // 同时使用系统日志
        switch level {
        case .debug:
            logger.debug("\(formattedMessage, privacy: .public)")
        case .info:
            logger.info("\(formattedMessage, privacy: .public)")
        case .warning:
            logger.notice("\(formattedMessage, privacy: .public)")
        case .error:
            logger.error("\(formattedMessage, privacy: .public)")
        }
    }
    
    /// 格式化日志消息
    func formatMessage(
        level: LogLevel,
        message: String,
        category: LogCategory,
        file: String,
        function: String,
        line: Int
    ) -> String {
        var components: [String] = []
        
        // 时间戳
        if showTimestamp {
            components.append("[\(dateFormatter.string(from: Date()))]")
        }
        
        // 日志级别
        components.append("[\(level.displayName)]")
        
        // 分类
        components.append("[\(category.emoji)\(category.rawValue)]")
        
        // 文件信息
        if showFileInfo {
            let fileName = URL(fileURLWithPath: file).lastPathComponent
            components.append("[\(fileName):\(line)]")
        }
        
        // 消息内容
        components.append(message)
        
        return components.joined(separator: " ")
    }
}

// MARK: - 预览和测试

#if DEBUG
import SwiftUI

/// 日志系统预览和测试
struct LoggerPreview {

    /// 测试所有日志级别
    static func testAllLogLevels() {
        AppLogger.debug("这是一条调试信息", category: .ui)
        AppLogger.info("应用启动完成", category: .general)
        AppLogger.warning("网络连接不稳定", category: .network)
        AppLogger.error("数据加载失败", category: .data)
    }

    /// 测试不同模块分类
    static func testCategories() {
        AppLogger.info("用户界面初始化", category: .ui)
        AppLogger.info("网络请求开始", category: .network)
        AppLogger.info("数据库操作", category: .data)
        AppLogger.info("用户认证", category: .auth)
        AppLogger.info("性能监控", category: .performance)
        AppLogger.info("通用日志", category: .general)
    }

    /// 测试配置选项
    static func testConfiguration() {
        // 保存原始配置
        let originalShowFileInfo = AppLogger.shared.showFileInfo
        let originalShowTimestamp = AppLogger.shared.showTimestamp
        let originalMinimumLevel = AppLogger.shared.minimumLogLevel

        // 测试不同配置
        AppLogger.shared.showFileInfo = false
        AppLogger.shared.showTimestamp = false
        AppLogger.info("无文件信息和时间戳的日志")

        AppLogger.shared.minimumLogLevel = .warning
        AppLogger.debug("这条调试信息不会显示")
        AppLogger.warning("这条警告信息会显示")

        // 恢复原始配置
        AppLogger.shared.showFileInfo = originalShowFileInfo
        AppLogger.shared.showTimestamp = originalShowTimestamp
        AppLogger.shared.minimumLogLevel = originalMinimumLevel
    }
}

#Preview("Logger Test") {
    VStack(spacing: 20) {
        Text("日志系统测试")
            .font(.title)
            .fontWeight(.bold)

        Button("测试所有日志级别") {
            LoggerPreview.testAllLogLevels()
        }
        .buttonStyle(.borderedProminent)

        Button("测试模块分类") {
            LoggerPreview.testCategories()
        }
        .buttonStyle(.bordered)

        Button("测试配置选项") {
            LoggerPreview.testConfiguration()
        }
        .buttonStyle(.bordered)

        Text("查看 Xcode 控制台输出")
            .font(.caption)
            .foregroundColor(.secondary)
    }
    .padding()
}
#endif
