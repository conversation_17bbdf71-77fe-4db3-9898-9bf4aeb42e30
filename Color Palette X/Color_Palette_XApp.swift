//
//  Color_Palette_XApp.swift
//  Color Palette X
//
//  Created by <PERSON><PERSON><PERSON> on 2025/7/12.
//

import SwiftUI

@main
struct Color_Palette_XApp: App {

    init() {
        // 配置日志系统
        setupLogger()
        AppLogger.info("Color Palette X 应用启动", category: .general)
    }

    var body: some Scene {
        WindowGroup {
            ContentView()
                .onAppear {
                    AppLogger.info("主窗口显示", category: .ui)
                }
        }
    }

    /// 配置日志系统
    private func setupLogger() {
        #if DEBUG
        AppLogger.shared.isEnabled = true
        AppLogger.shared.minimumLogLevel = .debug
        AppLogger.shared.showFileInfo = true
        AppLogger.shared.showTimestamp = true
        #else
        AppLogger.shared.isEnabled = true
        AppLogger.shared.minimumLogLevel = .info
        AppLogger.shared.showFileInfo = false
        AppLogger.shared.showTimestamp = true
        AppLogger.shared.disableInRelease = false
        #endif

        AppLogger.debug("日志系统配置完成", category: .general)
    }
}
