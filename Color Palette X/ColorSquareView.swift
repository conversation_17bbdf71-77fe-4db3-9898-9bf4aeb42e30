//
//  ColorSquareView.swift
//  Color Palette X
//
//  Created by <PERSON><PERSON><PERSON> on 2025/7/15.
//

import SwiftUI
import RandomColor

/// 颜色方块视图组件
///
/// 显示单个颜色的方块，包括：
/// - 颜色预览
/// - 十六进制值显示
/// - 点击复制功能
/// - 现代iOS设计风格
struct ColorSquareView: View {
    
    // MARK: - 属性
    
    /// 要显示的颜色
    let color: SwiftUIColor
    
    /// 是否显示十六进制值
    let showHexValue: Bool

    /// 方块大小
    let size: CGFloat
    
    /// 圆角半径
    let cornerRadius: CGFloat
    
    /// 点击状态
    @State private var isPressed = false
    
    /// 复制反馈状态
    @State private var showCopyFeedback = false
    
    // MARK: - 初始化方法
    
    /// 创建颜色方块视图
    /// - Parameters:
    ///   - color: 颜色
    ///   - showHexValue: 是否显示十六进制值，默认 true
    ///   - size: 方块大小，默认 80
    ///   - cornerRadius: 圆角半径，默认 12
    init(
        color: SwiftUIColor,
        showHexValue: Bool = true,
        size: CGFloat = 80,
        cornerRadius: CGFloat = 12
    ) {
        self.color = color
        self.showHexValue = showHexValue
        self.size = size
        self.cornerRadius = cornerRadius
    }
    
    // MARK: - 计算属性
    
    /// 十六进制颜色值
    private var hexValue: String {
        ColorUtils.hexString(from: color)
    }
    
    /// 是否为深色
    private var isDarkColor: Bool {
        ColorUtils.isDarkColor(color)
    }
    
    /// 文本颜色（根据背景色自动调整）
    private var textColor: SwiftUIColor {
        isDarkColor ? .white : .black
    }

    /// 阴影颜色
    private var shadowColor: SwiftUIColor {
        color.opacity(0.3)
    }
    
    // MARK: - 视图主体
    
    var body: some View {
        Button(action: {
            copyColorToClipboard()
        }) {
            VStack(spacing: 6) {
                // 主要颜色区域
                ZStack {
                    // 背景颜色
                    RoundedRectangle(cornerRadius: cornerRadius)
                        .fill(color)
                        .frame(width: size, height: size)

                    // 复制反馈
                    if showCopyFeedback {
                        ZStack {
                            RoundedRectangle(cornerRadius: cornerRadius)
                                .fill(.ultraThinMaterial)
                            
                            VStack(spacing: 4) {
                                Image(systemName: "checkmark.circle.fill")
                                    .font(.system(size: 20))
                                    .foregroundColor(.green)
                                
                                Text("已复制")
                                    .font(.system(size: 10, weight: .medium))
                                    .foregroundColor(.primary)
                            }
                        }
                        .frame(width: size, height: size)
                        .transition(.scale.combined(with: .opacity))
                    }
                }
                .shadow(
                    color: shadowColor,
                    radius: isPressed ? 4 : 8,
                    x: 0,
                    y: isPressed ? 2 : 4
                )
                .scaleEffect(isPressed ? 0.95 : 1.0)
                .animation(.easeInOut(duration: 0.1), value: isPressed)
                
                // 颜色信息
                VStack(spacing: 2) {
                    // 十六进制值
                    if showHexValue {
                        Text(hexValue)
                            .font(.system(size: 11, weight: .semibold, design: .monospaced))
                            .foregroundColor(.primary)
                            .lineLimit(1)
                    }
                }
                .frame(width: size)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
            if pressing {
                AppLogger.debug("颜色方块按下: \(hexValue)", category: .ui)
            }
        }, perform: {})
    }
    
    // MARK: - 私有方法
    
    /// 复制颜色到剪贴板
    private func copyColorToClipboard() {
        AppLogger.info("复制颜色到剪贴板: \(hexValue)", category: .ui)
        
        // 复制到剪贴板
        UIPasteboard.general.string = hexValue
        
        // 显示复制反馈
        withAnimation(.easeInOut(duration: 0.2)) {
            showCopyFeedback = true
        }
        
        // 延迟隐藏反馈
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            withAnimation(.easeInOut(duration: 0.2)) {
                showCopyFeedback = false
            }
        }
        
        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
    }
}



// MARK: - 预览

#if DEBUG
#Preview("ColorSquareView") {
    VStack(spacing: 20) {
        Text("颜色方块组件预览")
            .font(.title2)
            .fontWeight(.bold)
        
        // 单个颜色方块
        HStack(spacing: 16) {
            ColorSquareView(
                color: SwiftUIColor.red,
                size: 80
            )

            ColorSquareView(
                color: SwiftUIColor.blue,
                size: 80
            )

            ColorSquareView(
                color: SwiftUIColor.green,
                size: 80
            )

            ColorSquareView(
                color: SwiftUIColor.purple,
                size: 80
            )
        }
        
        Divider()
        
        // 网格布局预览
        Text("网格布局预览")
            .font(.headline)
        
        LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 16), count: 3), spacing: 16) {
            ForEach(0..<6, id: \.self) { _ in
                ColorSquareView(
                    color: SwiftUIColor(randomColor()),
                    size: 60
                )
            }
        }
        .padding()
    }
    .padding()
}
#endif
