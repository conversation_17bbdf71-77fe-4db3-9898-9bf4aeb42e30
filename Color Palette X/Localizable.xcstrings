{"sourceLanguage": "en", "strings": {"accessibility_checker": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Accessibility"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "无障碍检查"}}}}, "accessibility_tools": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Accessibility"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "无障碍工具"}}}}, "accessibility_tools_desc": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Check color contrast and accessibility"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "检查颜色对比度和无障碍性"}}}}, "app_features": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Key Features"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "核心功能"}}}}, "auto_scroll_active": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Auto Scrolling"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "自动滚动中"}}}}, "auto_scroll_disabled": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Auto Scroll Disabled"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "自动滚动已关闭"}}}}, "auto_scroll_pause": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Pause Auto Scroll"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "暂停自动滚动"}}}}, "auto_scroll_paused": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "<PERSON><PERSON> Paused"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "滚动已暂停"}}}}, "auto_scroll_resume": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Resume Auto Scroll"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "开启自动滚动"}}}}, "color_extraction": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Color Extraction"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "颜色提取"}}}}, "color_extraction_desc": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Extract colors from images"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "从图片中提取颜色"}}}}, "color_format_converter": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Format Converter"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "格式转换"}}}}, "color_harmony_generator": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Color Harmony"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "色彩和谐"}}}}, "color_palette_title": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Color Palette"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "颜色调色板"}}}}, "color_tools": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Color Tools"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "色彩工具"}}}}, "colors_count": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "%d colors"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "%d 个颜色"}}}}, "colors_count %lld": {}, "design_toolkit": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Design Toolkit"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "设计工具包"}}}}, "Discover powerful tools for your design workflow": {}, "favorites": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Favorites"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "收藏"}}}}, "favorites_description": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Save your favorite colors and gradients here"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "在这里保存您喜爱的颜色和渐变"}}}}, "format_conversion": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Format Conversion"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "格式转换"}}}}, "format_conversion_desc": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Convert between color formats"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "在不同颜色格式间转换"}}}}, "generating": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Generating..."}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "生成中..."}}}}, "gradient_creation": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Gradient Creation"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "渐变创建"}}}}, "gradient_creation_desc": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Create beautiful gradients with ease"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "轻松创建美丽的渐变效果"}}}}, "gradient_presets": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Gradient Presets"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "渐变预设"}}}}, "home": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Home"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "主页"}}}}, "image_color_extractor": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Image Colors"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "图片取色"}}}}, "profile": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Profile"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "个人资料"}}}}, "profile_description": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Manage your settings and preferences"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "管理您的设置和偏好"}}}}, "regenerate_colors": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Regenerate Colors"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "重新生成颜色"}}}}, "text_readability_tester": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Text Readability"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "文本可读性"}}}}, "welcome": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Welcome"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "欢迎"}}}}, "welcome_subtitle": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Professional color tools for designers"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "面向设计师的专业色彩工具"}}}}, "welcome_title": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Color Palette X"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "Color Palette X"}}}}, "add_to_favorites": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Add to Favorites"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "添加到收藏"}}}}, "ai_powered_extraction": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "AI-powered color analysis"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "AI 驱动的颜色分析"}}}}, "camera": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Camera"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "相机"}}}}, "camera_accessibility_hint": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Open camera to take a new photo"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "打开相机拍摄新照片"}}}}, "camera_not_available": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Camera not available"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "相机不可用"}}}}, "camera_permission_denied": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Camera permission denied"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "相机权限被拒绝"}}}}, "camera_permission_message": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Please allow camera access in Settings to use the photo capture feature"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "请在设置中允许访问相机以使用拍照功能"}}}}, "camera_permission_required": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Camera Permission Required"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "需要相机权限"}}}}, "cancel": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Cancel"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "取消"}}}}, "cancel_accessibility_hint": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Cancel image selection"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "取消图片选择"}}}}, "cancel_selection_hint": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Cancel image selection and close popup"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "取消图片选择并关闭弹窗"}}}}, "capture_new_image": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Capture a new image"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "拍摄新图片"}}}}, "choose_existing_image": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Choose from existing images"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "从现有图片中选择"}}}}, "choose_from_library": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Choose from photo library"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "从相册中选择"}}}}, "choose_how_to_add_image": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Choose how you'd like to add an image"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "选择您想要添加图片的方式"}}}}, "clear_cache": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "<PERSON>ache"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "清理缓存"}}}}, "color_card_hint": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Tap to copy color value, long press for details"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "点击复制颜色值，长按查看详细信息"}}}}, "color_details": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Color Details"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "颜色详情"}}}}, "color_extraction_error": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Color extraction failed"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "颜色提取失败"}}}}, "color_formats": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Color Formats"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "颜色格式"}}}}, "copied": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "<PERSON>pied"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "已复制"}}}}, "data_extraction_failed": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Data extraction failed"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "数据提取失败"}}}}, "done": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Done"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "完成"}}}}, "error": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Error"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "错误"}}}}, "export_color_palette": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Export and share palettes"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "导出和分享调色板"}}}}, "export_palette": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Export Palette"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "导出调色板"}}}}, "extract_colors": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Extract Colors"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "提取颜色"}}}}, "extract_colors_from_images": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Extract Colors from Images"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "从图片中提取颜色"}}}}, "extracted_colors": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Extracted Colors"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "提取的颜色"}}}}, "extracting_colors": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Extracting Colors..."}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "正在提取颜色..."}}}}, "extraction_cancelled": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Color extraction cancelled"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "颜色提取已取消"}}}}, "extraction_failed": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Extraction Failed"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "提取失败"}}}}, "extraction_parameters": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Extraction Parameters"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "提取参数"}}}}, "extraction_timeout": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Color extraction timeout"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "颜色提取超时"}}}}, "features": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Features"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "功能特色"}}}}, "from_library": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "From Library"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "从相册"}}}}, "hex_format": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "HEX"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "十六进制"}}}}, "hsb_format": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "HSB"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "HSB"}}}}, "hsl_format": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "HSL"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "HSL"}}}}, "image_info": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Image Info"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "图片信息"}}}}, "image_load_error": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Image loading failed"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "图片加载失败"}}}}, "image_zoom_hint": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Double tap to reset zoom, drag to move image"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "双击重置缩放，拖拽移动图片"}}}}, "memory_error": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Insufficient memory"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "内存不足"}}}}, "memory_warning": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Memory warning"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "内存不足警告"}}}}, "more_options": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "More Options"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "更多选项"}}}}, "no_colors_extracted": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "No Colors Extracted"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "未提取到颜色"}}}}, "no_image_provided": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "No image provided"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "未提供图片"}}}}, "no_image_selected": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "No Image Selected"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "未选择图片"}}}}, "ok": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "OK"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "确定"}}}}, "photo_library": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Photo Library"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "相册"}}}}, "photo_library_accessibility_hint": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Choose photo from library"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "从相册中选择照片"}}}}, "please_wait": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Please wait..."}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "请稍候..."}}}}, "reselect": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Reselect"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "重选"}}}}, "retry": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Retry"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "重试"}}}}, "rgb_format": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "RGB"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "RGB"}}}}, "scale_label": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Scale:"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "缩放:"}}}}, "select_image": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Select Image"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "选择图片"}}}}, "select_image_for_color_extraction": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Select image for color extraction"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "选择图片进行颜色提取"}}}}, "select_image_source": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Select Image Source"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "选择图片来源"}}}}, "select_image_to_extract": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Select an image to extract colors"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "选择图片以提取颜色"}}}}, "select_new_image": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Select New Image"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "选择新图片"}}}}, "selected_image": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Selected Image"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "选中的图片"}}}}, "settings": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Settings"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "设置"}}}}, "share_color": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Share Color"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "分享颜色"}}}}, "share_results": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Share Results"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "分享结果"}}}}, "size_label": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Size:"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "尺寸:"}}}}, "take_new_photo": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Take a new photo"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "拍摄新照片"}}}}, "take_photo": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Take Photo"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "拍照"}}}}, "timeout_error": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Processing timeout"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "处理超时"}}}}, "unsupported_format_error": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Unsupported image format"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "不支持的图片格式"}}}}, "choose_image_source_description": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Take a photo or choose from your library to extract beautiful color palettes"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "拍摄照片或从相册中选择，提取美丽的调色板"}}}}, "已复制": {}}, "version": "1.1"}