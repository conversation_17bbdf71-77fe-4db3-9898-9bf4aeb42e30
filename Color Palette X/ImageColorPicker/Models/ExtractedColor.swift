//
//  ExtractedColor.swift
//  Color Palette X
//
//  Created by <PERSON><PERSON> on 2025/7/27.
//

import SwiftUI
import UIKit

/// 提取的颜色模型
/// 
/// 存储从图片中提取的颜色信息，包括颜色值、权重和十六进制表示
struct ExtractedColor: Identifiable, Hashable {
    let id = UUID()
    let color: SwiftUIColor
    let weight: Double
    let hexString: String
    
    /// 从 UIColor 初始化
    /// - Parameter uiColor: UIColor 对象，alpha 值表示颜色权重
    init(uiColor: UIColor) {
        // 提取 RGBA 值
        var red: CGFloat = 0
        var green: CGFloat = 0
        var blue: CGFloat = 0
        var alpha: CGFloat = 0
        
        uiColor.getRed(&red, green: &green, blue: &blue, alpha: &alpha)
        
        // 创建 SwiftUI Color（不透明）
        self.color = SwiftUIColor(red: red, green: green, blue: blue)
        
        // 权重来自 alpha 通道
        self.weight = Double(alpha)
        
        // 生成十六进制字符串
        let r = Int(red * 255)
        let g = Int(green * 255)
        let b = Int(blue * 255)
        self.hexString = String(format: "#%02X%02X%02X", r, g, b)
    }
    
    /// 从 RGB 值和权重初始化
    /// - Parameters:
    ///   - red: 红色分量 (0-1)
    ///   - green: 绿色分量 (0-1)
    ///   - blue: 蓝色分量 (0-1)
    ///   - weight: 颜色权重 (0-1)
    init(red: Double, green: Double, blue: Double, weight: Double) {
        self.color = SwiftUIColor(red: red, green: green, blue: blue)
        self.weight = weight
        
        let r = Int(red * 255)
        let g = Int(green * 255)
        let b = Int(blue * 255)
        self.hexString = String(format: "#%02X%02X%02X", r, g, b)
    }
    
    /// 权重百分比字符串
    var weightPercentage: String {
        return String(format: "%.1f%%", weight * 100)
    }
}

// MARK: - 预览支持

#if DEBUG
extension ExtractedColor {
    /// 创建示例颜色用于预览
    static let samples: [ExtractedColor] = [
        ExtractedColor(red: 0.2, green: 0.4, blue: 0.8, weight: 0.85),
        ExtractedColor(red: 0.8, green: 0.2, blue: 0.3, weight: 0.72),
        ExtractedColor(red: 0.3, green: 0.7, blue: 0.2, weight: 0.65),
        ExtractedColor(red: 0.9, green: 0.7, blue: 0.1, weight: 0.58),
        ExtractedColor(red: 0.5, green: 0.3, blue: 0.8, weight: 0.45),
        ExtractedColor(red: 0.1, green: 0.1, blue: 0.1, weight: 0.32),
        ExtractedColor(red: 0.9, green: 0.9, blue: 0.9, weight: 0.28),
        ExtractedColor(red: 0.6, green: 0.4, blue: 0.2, weight: 0.15)
    ]
}
#endif