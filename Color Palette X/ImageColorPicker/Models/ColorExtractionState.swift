//
//  ColorExtractionState.swift
//  Color Palette X
//
//  Created by <PERSON><PERSON> on 2025/7/27.
//

import Foundation

/// 颜色提取状态枚举
/// 
/// 管理颜色提取过程的不同状态
enum ColorExtractionState: Equatable {
    case idle
    case loading
    case success([ExtractedColor])
    case error(String)
    
    /// 是否正在加载
    var isLoading: Bool {
        if case .loading = self {
            return true
        }
        return false
    }
    
    /// 是否有错误
    var hasError: Bool {
        if case .error = self {
            return true
        }
        return false
    }
    
    /// 获取错误消息
    var errorMessage: String? {
        if case .error(let message) = self {
            return message
        }
        return nil
    }
    
    /// 获取提取的颜色
    var extractedColors: [ExtractedColor] {
        if case .success(let colors) = self {
            return colors
        }
        return []
    }
    
    /// 是否有成功的结果
    var hasResults: Bool {
        if case .success(let colors) = self {
            return !colors.isEmpty
        }
        return false
    }
}

// MARK: - 本地化错误消息

extension ColorExtractionState {
    /// 常见错误状态的工厂方法
    static func imageLoadError() -> ColorExtractionState {
        return .error(NSLocalizedString("image_load_error", comment: "图片加载失败"))
    }
    
    static func extractionError() -> ColorExtractionState {
        return .error(NSLocalizedString("color_extraction_error", comment: "颜色提取失败"))
    }
    
    static func unsupportedFormatError() -> ColorExtractionState {
        return .error(NSLocalizedString("unsupported_format_error", comment: "不支持的图片格式"))
    }
    
    static func memoryError() -> ColorExtractionState {
        return .error(NSLocalizedString("memory_error", comment: "内存不足"))
    }
    
    static func timeoutError() -> ColorExtractionState {
        return .error(NSLocalizedString("timeout_error", comment: "处理超时"))
    }
}