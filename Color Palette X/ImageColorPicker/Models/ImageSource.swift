//
//  ImageSource.swift
//  Color Palette X
//
//  Created by <PERSON><PERSON> on 2025/7/27.
//

import SwiftUI

/// 图片来源枚举
/// 
/// 定义用户可以选择的图片来源选项
enum ImageSource: CaseIterable {
    case camera
    case photoLibrary
    case cancel
    
    /// 选项的本地化标题
    var title: LocalizedStringKey {
        switch self {
        case .camera:
            return LocalizedStringKey("camera")
        case .photoLibrary:
            return LocalizedStringKey("photo_library")
        case .cancel:
            return LocalizedStringKey("cancel")
        }
    }
    
    /// 选项的图标
    var icon: String {
        switch self {
        case .camera:
            return "camera.fill"
        case .photoLibrary:
            return "photo.fill"
        case .cancel:
            return "xmark.circle.fill"
        }
    }
    
    /// 选项的颜色
    var color: Color {
        switch self {
        case .camera:
            return .blue
        case .photoLibrary:
            return .green
        case .cancel:
            return .secondary
        }
    }
}