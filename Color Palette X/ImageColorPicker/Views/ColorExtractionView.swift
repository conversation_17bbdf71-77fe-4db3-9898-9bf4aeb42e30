//
//  ColorExtractionView.swift
//  Color Palette X
//
//  Created by <PERSON><PERSON> on 2025/7/27.
//

import SwiftUI

/// 颜色提取主界面
/// 
/// 显示选中的图片和提取的颜色，分为上下两个区域
struct ColorExtractionView: View {
    
    // MARK: - 属性
    
    /// 视图模型
    @State private var viewModel: ColorExtractionViewModel
    
    /// 重新选择图片的回调
    let onSelectNewImage: () -> Void
    
    // MARK: - 初始化
    
    init(
        image: UIImage,
        onSelectNewImage: @escaping () -> Void
    ) {
        self._viewModel = State(initialValue: ColorExtractionViewModel(image: image))
        self.onSelectNewImage = onSelectNewImage
        
        AppLogger.info("ColorExtractionView 初始化", category: .ui)
    }
    
    // MARK: - 视图
    
    var body: some View {
        GeometryReader { geometry in
            VStack(spacing: 0) {
                // 上部分：图片显示区域
                imageSection(geometry: geometry)
                
                // 分隔线
                Divider()
                    .background(Color.secondary.opacity(0.3))
                
                // 下部分：颜色显示区域
                colorSection(geometry: geometry)
            }
        }
        .navigationTitle(LocalizedStringKey("color_extraction"))
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                toolbarMenu
            }
        }
        .onAppear {
            AppLogger.debug("ColorExtractionView 显示", category: .ui)
        }
        .onDisappear {
            AppLogger.debug("ColorExtractionView 隐藏", category: .ui)
            viewModel.cleanup()
        }
    }
    
    // MARK: - 子视图
    
    /// 图片显示区域
    /// - Parameter geometry: 几何信息
    /// - Returns: 图片区域视图
    private func imageSection(geometry: GeometryProxy) -> some View {
        let imageHeight = geometry.size.height * 0.4 // 占用40%的高度
        
        return VStack(spacing: 0) {
            if let image = viewModel.image {
                ImageDisplayView(
                    image: image,
                    height: imageHeight,
                    onSelectNewImage: onSelectNewImage
                )
            } else {
                EmptyImageView(height: imageHeight)
            }
        }
        .frame(height: imageHeight)
    }
    
    /// 颜色显示区域
    /// - Parameter geometry: 几何信息
    /// - Returns: 颜色区域视图
    private func colorSection(geometry: GeometryProxy) -> some View {
        let colorHeight = geometry.size.height * 0.6 // 占用60%的高度
        
        return VStack(spacing: 0) {
            ColorDisplayView(
                extractionState: viewModel.extractionState,
                height: colorHeight,
                onRetry: {
                    Task {
                        await viewModel.retryExtraction()
                    }
                }
            )
        }
        .frame(height: colorHeight)
    }
    
    /// 工具栏菜单
    private var toolbarMenu: some View {
        Menu {
            // 重新选择图片
            Button(action: onSelectNewImage) {
                Label(LocalizedStringKey("select_new_image"), systemImage: "photo.badge.plus")
            }
            
            // 参数设置
            Button(action: {
                viewModel.toggleParameterSettings()
            }) {
                Label(LocalizedStringKey("extraction_parameters"), systemImage: "slider.horizontal.3")
            }
            
            // 导出调色板
            if viewModel.hasResults {
                Button(action: exportColorPalette) {
                    Label(LocalizedStringKey("export_palette"), systemImage: "square.and.arrow.up")
                }
            }
            
            // 清理缓存
            Button(action: {
                viewModel.cleanup()
            }) {
                Label(LocalizedStringKey("clear_cache"), systemImage: "trash")
            }
        } label: {
            Image(systemName: "ellipsis.circle")
                .font(.title3)
                .foregroundStyle(
                    LinearGradient(
                        colors: [.indigo, .purple],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
        }
        .accessibilityLabel(LocalizedStringKey("more_options"))
    }
    
    // MARK: - 方法
    
    /// 导出调色板
    private func exportColorPalette() {
        let paletteData = viewModel.exportColorPalette()
        AppLogger.info("导出调色板数据: \(paletteData)", category: .data)
        
        // 这里可以添加实际的导出逻辑，比如保存到文件或分享
        // 暂时只是记录日志
    }
}

// MARK: - 图片显示视图

/// 图片显示视图组件
private struct ImageDisplayView: View {
    let image: UIImage
    let height: CGFloat
    let onSelectNewImage: () -> Void
    
    @State private var imageScale: CGFloat = 1.0
    @State private var imageOffset: CGSize = .zero
    @State private var showImageInfo = false
    
    var body: some View {
        ZStack {
            // 背景渐变
            backgroundGradient
            
            // 主图片容器
            imageContainer
            
            // 控制按钮覆盖层
            controlOverlay
            
            // 图片信息弹窗
            if showImageInfo {
                imageInfoOverlay
            }
        }
        .frame(height: height)
        .clipped()
    }
    
    /// 背景渐变
    private var backgroundGradient: some View {
        LinearGradient(
            gradient: Gradient(colors: [
                Color(.systemBackground),
                Color.secondary.opacity(0.1),
                Color.indigo.opacity(0.05)
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
    
    /// 图片容器
    private var imageContainer: some View {
        GeometryReader { geometry in
            Image(uiImage: image)
                .resizable()
                .aspectRatio(contentMode: .fit)
                .scaleEffect(imageScale)
                .offset(imageOffset)
                .animation(.interactiveSpring(), value: imageOffset)
                .gesture(zoomAndPanGesture)
                .onTapGesture(count: 2) {
                    // 双击重置缩放
                    withAnimation(.spring()) {
                        imageScale = 1.0
                        imageOffset = .zero
                    }
                }
                .accessibilityLabel(LocalizedStringKey("selected_image"))
                .accessibilityHint(NSLocalizedString("image_zoom_hint", comment: "双击重置缩放，拖拽移动图片"))
        }
    }
    
    /// 缩放和拖拽手势
    private var zoomAndPanGesture: some Gesture {
        SimultaneousGesture(
            // 缩放手势
            MagnificationGesture()
                .onChanged { value in
                    let newScale = max(0.5, min(3.0, value))
                    imageScale = newScale
                }
                .onEnded { _ in
                    // 限制缩放范围
                    withAnimation(.spring()) {
                        if imageScale < 0.8 {
                            imageScale = 0.8
                        } else if imageScale > 2.5 {
                            imageScale = 2.5
                        }
                    }
                },
            
            // 拖拽手势
            DragGesture()
                .onChanged { value in
                    imageOffset = value.translation
                }
                .onEnded { _ in
                    // 回弹到中心
                    withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                        imageOffset = .zero
                    }
                }
        )
    }
    
    /// 控制按钮覆盖层
    private var controlOverlay: some View {
        VStack {
            // 顶部控制栏
            HStack {
                // 图片信息按钮
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        showImageInfo.toggle()
                    }
                }) {
                    Image(systemName: "info.circle")
                        .font(.title3)
                        .foregroundColor(.white)
                        .padding(10)
                        .background(
                            Circle()
                                .fill(.ultraThinMaterial)
                                .shadow(color: .black.opacity(0.2), radius: 4)
                        )
                }
                .accessibilityLabel(LocalizedStringKey("image_info"))
                
                Spacer()
                
                // 重新选择按钮
                Button(action: onSelectNewImage) {
                    HStack(spacing: 6) {
                        Image(systemName: "photo.badge.plus")
                            .font(.system(size: 16, weight: .medium))
                        Text(LocalizedStringKey("reselect"))
                            .font(.system(size: 14, weight: .medium))
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(
                        Capsule()
                            .fill(.ultraThinMaterial)
                            .shadow(color: .black.opacity(0.2), radius: 4)
                    )
                }
                .accessibilityLabel(LocalizedStringKey("select_new_image"))
            }
            
            Spacer()
            
            // 底部缩放指示器
            if imageScale != 1.0 {
                HStack {
                    Spacer()
                    Text("\(Int(imageScale * 100))%")
                        .font(.caption)
                        .foregroundColor(.white)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(
                            Capsule()
                                .fill(.ultraThinMaterial)
                        )
                }
                .transition(.opacity.combined(with: .scale))
            }
        }
        .padding(16)
    }
    
    /// 图片信息覆盖层
    private var imageInfoOverlay: some View {
        VStack {
            HStack {
                VStack(alignment: .leading, spacing: 8) {
                    Text(LocalizedStringKey("image_info"))
                        .font(.headline)
                        .foregroundColor(.white)
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text(LocalizedStringKey("size_label"))
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.8))
                        Text("\(Int(image.size.width)) × \(Int(image.size.height))")
                            .font(.subheadline)
                            .foregroundColor(.white)
                        
                        Text(LocalizedStringKey("scale_label"))
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.8))
                        Text("@\(Int(image.scale))x")
                            .font(.subheadline)
                            .foregroundColor(.white)
                    }
                }
                
                Spacer()
                
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        showImageInfo = false
                    }
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.title3)
                        .foregroundColor(.white.opacity(0.8))
                }
            }
            
            Spacer()
        }
        .padding(16)
        .background(
            LinearGradient(
                gradient: Gradient(colors: [
                    .black.opacity(0.6),
                    .clear
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
        )
        .transition(.opacity.combined(with: .move(edge: .top)))
    }
}

// MARK: - 空图片视图

/// 空图片状态视图
private struct EmptyImageView: View {
    let height: CGFloat
    
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "photo")
                .font(.system(size: 60))
                .foregroundColor(.secondary)
            
            Text(LocalizedStringKey("no_image_selected"))
                .font(.title3)
                .foregroundColor(.secondary)
        }
        .frame(height: height)
        .frame(maxWidth: .infinity)
        .background(Color(.systemGroupedBackground))
    }
}

// MARK: - 颜色显示视图

/// 颜色显示视图组件
private struct ColorDisplayView: View {
    let extractionState: ColorExtractionState
    let height: CGFloat
    let onRetry: () -> Void
    
    var body: some View {
        VStack(spacing: 0) {
            // 标题栏
            colorSectionHeader
            
            // 内容区域
            ScrollView {
                colorContent
                    .padding(.horizontal, 16)
                    .padding(.bottom, 16)
            }
        }
        .frame(height: height)
        .background(Color(.systemGroupedBackground))
    }
    
    /// 颜色区域标题
    private var colorSectionHeader: some View {
        HStack {
            Text(LocalizedStringKey("extracted_colors"))
                .font(.headline)
                .foregroundColor(.primary)
            
            Spacer()
            
            if extractionState.hasResults {
                Text("\(extractionState.extractedColors.count)")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill(Color.secondary.opacity(0.2))
                    )
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color(.systemBackground))
    }
    
    /// 颜色内容
    @ViewBuilder
    private var colorContent: some View {
        switch extractionState {
        case .idle:
            EmptyColorView()
            
        case .loading:
            LoadingColorView()
            
        case .success(let colors):
            ColorGridView(colors: colors)
            
        case .error(let message):
            ErrorColorView(message: message, onRetry: onRetry)
        }
    }
}

// MARK: - 预览

#Preview("ColorExtractionView") {
    NavigationStack {
        if let testImage = UIImage.createTestColorImage() {
            ColorExtractionView(
                image: testImage,
                onSelectNewImage: {
                    print("重新选择图片")
                }
            )
        } else {
            Text("无法创建测试图片")
        }
    }
}

#Preview("ColorExtractionView - Dark Mode") {
    NavigationStack {
        if let testImage = UIImage.createTestColorImage() {
            ColorExtractionView(
                image: testImage,
                onSelectNewImage: {
                    print("重新选择图片")
                }
            )
        } else {
            Text("无法创建测试图片")
        }
    }
    .preferredColorScheme(.dark)
}