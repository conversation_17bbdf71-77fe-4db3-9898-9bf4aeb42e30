//
//  ImageSourceBottomSheet.swift
//  Color Palette X
//
//  Created by <PERSON><PERSON> on 2025/7/27.
//

import SwiftUI

/// 图片来源选择底部弹窗
/// 
/// 提供拍照、相册、取消三个选项的底部弹窗界面
struct ImageSourceBottomSheet: View {
    
    // MARK: - 属性
    
    /// 选择回调
    let onSelection: (ImageSource) -> Void
    
    /// 关闭回调
    let onDismiss: () -> Void
    
    // MARK: - 状态
    
    @State private var selectedOption: ImageSource?
    
    // MARK: - 初始化
    
    init(
        onSelection: @escaping (ImageSource) -> Void,
        onDismiss: @escaping () -> Void = {}
    ) {
        self.onSelection = onSelection
        self.onDismiss = onDismiss
        
        AppLogger.info("ImageSourceBottomSheet 初始化", category: .ui)
    }
    
    // MARK: - 视图
    
    var body: some View {
        VStack(spacing: 0) {
            // 拖拽指示器
            dragIndicator
            
            // 标题区域
            titleSection
            
            // 选项列表
            optionsSection
            
            // 底部安全区域
            bottomSafeArea
        }
        .background(backgroundView)
        .onAppear {
            AppLogger.debug("ImageSourceBottomSheet 显示", category: .ui)
        }
        .onDisappear {
            AppLogger.debug("ImageSourceBottomSheet 隐藏", category: .ui)
        }
    }
    
    // MARK: - 子视图
    
    /// 拖拽指示器
    private var dragIndicator: some View {
        RoundedRectangle(cornerRadius: 2.5)
            .fill(Color.secondary.opacity(0.5))
            .frame(width: 40, height: 5)
            .padding(.top, 12)
            .padding(.bottom, 8)
    }
    
    /// 标题区域
    private var titleSection: some View {
        VStack(spacing: 8) {
            Text(LocalizedStringKey("select_image_source"))
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
            
            Text(LocalizedStringKey("choose_how_to_add_image"))
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(.horizontal, 20)
        .padding(.bottom, 24)
    }
    
    /// 选项列表
    private var optionsSection: some View {
        VStack(spacing: 12) {
            ForEach([ImageSource.camera, ImageSource.photoLibrary], id: \.self) { source in
                OptionButton(
                    source: source,
                    isSelected: selectedOption == source,
                    onTap: {
                        handleOptionSelection(source)
                    }
                )
            }
            
            // 取消按钮
            CancelButton {
                handleOptionSelection(.cancel)
            }
        }
        .padding(.horizontal, 20)
        .padding(.bottom, 20)
    }
    
    /// 底部安全区域
    private var bottomSafeArea: some View {
        Color.clear
            .frame(height: 0)
            .safeAreaInset(edge: .bottom) {
                Color.clear.frame(height: 0)
            }
    }
    
    /// 背景视图
    private var backgroundView: some View {
        RoundedRectangle(cornerRadius: 20, style: .continuous)
            .fill(.regularMaterial)
            .shadow(color: .black.opacity(0.1), radius: 20, x: 0, y: -5)
    }
    
    // MARK: - 方法
    
    /// 处理选项选择
    /// - Parameter source: 选择的图片来源
    private func handleOptionSelection(_ source: ImageSource) {
        AppLogger.info("用户选择图片来源: \(source)", category: .ui)
        
        selectedOption = source
        
        // 添加触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
        
        // 延迟执行以显示选中状态
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            onSelection(source)
            
            if source == .cancel {
                onDismiss()
            }
        }
    }
}

// MARK: - 选项按钮

/// 选项按钮组件
private struct OptionButton: View {
    let source: ImageSource
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 16) {
                // 图标
                ZStack {
                    Circle()
                        .fill(source.color.opacity(0.15))
                        .frame(width: 50, height: 50)
                    
                    Image(systemName: source.icon)
                        .font(.system(size: 22, weight: .medium))
                        .foregroundColor(source.color)
                }
                
                // 文本
                VStack(alignment: .leading, spacing: 2) {
                    Text(source.title)
                        .font(.system(size: 17, weight: .medium))
                        .foregroundColor(.primary)
                    
                    Text(getSourceDescription(source))
                        .font(.system(size: 14))
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // 选中指示器
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 20))
                        .foregroundColor(source.color)
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(isSelected ? source.color.opacity(0.1) : Color.clear)
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(
                                isSelected ? source.color.opacity(0.3) : Color.secondary.opacity(0.2),
                                lineWidth: 1
                            )
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 0.98 : 1.0)
        .animation(.easeInOut(duration: 0.1), value: isSelected)
        .accessibilityLabel(source.title)
        .accessibilityHint(getAccessibilityHint(source))
    }
    
    /// 获取来源描述
    /// - Parameter source: 图片来源
    /// - Returns: 描述文本
    private func getSourceDescription(_ source: ImageSource) -> LocalizedStringKey {
        switch source {
        case .camera:
            return LocalizedStringKey("take_new_photo")
        case .photoLibrary:
            return LocalizedStringKey("choose_from_library")
        case .cancel:
            return LocalizedStringKey("cancel_selection")
        }
    }
    
    /// 获取可访问性提示
    /// - Parameter source: 图片来源
    /// - Returns: 可访问性提示
    private func getAccessibilityHint(_ source: ImageSource) -> String {
        switch source {
        case .camera:
            return NSLocalizedString("camera_accessibility_hint", comment: "打开相机拍摄新照片")
        case .photoLibrary:
            return NSLocalizedString("photo_library_accessibility_hint", comment: "从相册中选择照片")
        case .cancel:
            return NSLocalizedString("cancel_accessibility_hint", comment: "取消图片选择")
        }
    }
}

// MARK: - 取消按钮

/// 取消按钮组件
private struct CancelButton: View {
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            Text(LocalizedStringKey("cancel"))
                .font(.system(size: 17, weight: .medium))
                .foregroundColor(.secondary)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 16)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color.secondary.opacity(0.1))
                )
        }
        .buttonStyle(PlainButtonStyle())
        .accessibilityLabel(LocalizedStringKey("cancel"))
        .accessibilityHint(NSLocalizedString("cancel_selection_hint", comment: "取消图片选择并关闭弹窗"))
    }
}

// MARK: - 预览

#Preview("ImageSourceBottomSheet") {
    ZStack {
        Color.black.opacity(0.3)
            .ignoresSafeArea()
        
        VStack {
            Spacer()
            
            ImageSourceBottomSheet(
                onSelection: { source in
                    print("选择了: \(source)")
                },
                onDismiss: {
                    print("关闭弹窗")
                }
            )
        }
    }
}

#Preview("ImageSourceBottomSheet - Dark Mode") {
    ZStack {
        Color.black.opacity(0.3)
            .ignoresSafeArea()
        
        VStack {
            Spacer()
            
            ImageSourceBottomSheet(
                onSelection: { source in
                    print("选择了: \(source)")
                },
                onDismiss: {
                    print("关闭弹窗")
                }
            )
        }
    }
    .preferredColorScheme(.dark)
}