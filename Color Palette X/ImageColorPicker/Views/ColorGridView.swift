//
//  ColorGridView.swift
//  Color Palette X
//
//  Created by <PERSON><PERSON> on 2025/7/27.
//

import SwiftUI

/// 颜色网格显示视图
/// 
/// 以网格形式显示提取的颜色，支持点击复制和详细信息查看
struct ColorGridView: View {
    
    // MARK: - 属性
    
    let colors: [ExtractedColor]
    
    // MARK: - 状态
    
    @State private var selectedColor: ExtractedColor?
    @State private var showColorDetail = false
    @State private var copiedColorId: UUID?
    
    // MARK: - 网格配置
    
    private let columns = [
        GridItem(.flexible(), spacing: 12),
        GridItem(.flexible(), spacing: 12)
    ]
    
    // MARK: - 视图
    
    var body: some View {
        LazyVGrid(columns: columns, spacing: 16) {
            ForEach(colors) { color in
                ColorCard(
                    color: color,
                    isSelected: selectedColor?.id == color.id,
                    isCopied: copiedColorId == color.id,
                    onTap: {
                        handleColorTap(color)
                    },
                    onLongPress: {
                        handleColorLongPress(color)
                    }
                )
            }
        }
        .sheet(isPresented: $showColorDetail) {
            if let selectedColor = selectedColor {
                ColorDetailSheet(color: selectedColor)
            }
        }
        .onAppear {
            AppLogger.debug("ColorGridView 显示，包含 \(colors.count) 个颜色", category: .ui)
        }
    }
    
    // MARK: - 方法
    
    /// 处理颜色点击
    /// - Parameter color: 被点击的颜色
    private func handleColorTap(_ color: ExtractedColor) {
        AppLogger.info("用户点击颜色: \(color.hexString)", category: .ui)
        
        // 复制到剪贴板
        UIPasteboard.general.string = color.hexString
        
        // 显示复制反馈
        withAnimation(.easeInOut(duration: 0.3)) {
            copiedColorId = color.id
        }
        
        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
        
        // 清除复制状态
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            withAnimation(.easeInOut(duration: 0.3)) {
                copiedColorId = nil
            }
        }
    }
    
    /// 处理颜色长按
    /// - Parameter color: 被长按的颜色
    private func handleColorLongPress(_ color: ExtractedColor) {
        AppLogger.info("用户长按颜色: \(color.hexString)", category: .ui)
        
        selectedColor = color
        showColorDetail = true
        
        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }
}

// MARK: - 颜色卡片

/// 颜色卡片组件
private struct ColorCard: View {
    let color: ExtractedColor
    let isSelected: Bool
    let isCopied: Bool
    let onTap: () -> Void
    let onLongPress: () -> Void
    
    @State private var isPressed = false
    
    var body: some View {
        VStack(spacing: 12) {
            // 颜色方块
            colorSquare
            
            // 颜色信息
            colorInfo
        }
        .padding(16)
        .background(cardBackground)
        .overlay(cardBorder)
        .scaleEffect(isPressed ? 0.95 : 1.0)
        .animation(.easeInOut(duration: 0.1), value: isPressed)
        .onTapGesture {
            onTap()
        }
        .onLongPressGesture(minimumDuration: 0.5) {
            onLongPress()
        }
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = pressing
            }
        }, perform: {})
        .accessibilityElement(children: .combine)
        .accessibilityLabel("\(color.hexString), 权重 \(color.weightPercentage)")
        .accessibilityHint(NSLocalizedString("color_card_hint", comment: "点击复制颜色值，长按查看详细信息"))
    }
    
    /// 颜色方块
    private var colorSquare: some View {
        ZStack {
            // 主颜色
            RoundedRectangle(cornerRadius: 12)
                .fill(color.color)
                .frame(height: 80)
            
            // 复制状态覆盖层
            if isCopied {
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
                    .frame(height: 80)
                    .overlay(
                        VStack(spacing: 4) {
                            Image(systemName: "checkmark.circle.fill")
                                .font(.title2)
                                .foregroundColor(.green)
                            
                            Text(LocalizedStringKey("copied"))
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.primary)
                        }
                    )
                    .transition(.opacity.combined(with: .scale))
            }
            
            // 权重指示器
            VStack {
                HStack {
                    Spacer()
                    Text(color.weightPercentage)
                        .font(.caption2)
                        .fontWeight(.semibold)
                        .foregroundColor(ColorUtils.isDarkColor(color.color) ? .white : .black)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(
                            Capsule()
                                .fill(.ultraThinMaterial)
                        )
                }
                Spacer()
            }
            .padding(8)
        }
        .shadow(color: color.color.opacity(0.3), radius: 8, x: 0, y: 4)
    }
    
    /// 颜色信息
    private var colorInfo: some View {
        VStack(spacing: 4) {
            Text(color.hexString)
                .font(.system(size: 14, weight: .semibold, design: .monospaced))
                .foregroundColor(.primary)
            
            Text("权重: \(color.weightPercentage)")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    
    /// 卡片背景
    private var cardBackground: some View {
        RoundedRectangle(cornerRadius: 16)
            .fill(.regularMaterial)
            .shadow(
                color: .black.opacity(0.1),
                radius: isPressed ? 4 : 8,
                x: 0,
                y: isPressed ? 2 : 4
            )
    }
    
    /// 卡片边框
    private var cardBorder: some View {
        RoundedRectangle(cornerRadius: 16)
            .stroke(
                isSelected ? color.color.opacity(0.5) : Color.clear,
                lineWidth: 2
            )
    }
}

// MARK: - 空状态视图

/// 空颜色状态视图
struct EmptyColorView: View {
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "paintpalette")
                .font(.system(size: 50))
                .foregroundColor(.secondary)
            
            Text(LocalizedStringKey("no_colors_extracted"))
                .font(.title3)
                .foregroundColor(.secondary)
            
            Text(LocalizedStringKey("select_image_to_extract"))
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(40)
    }
}

// MARK: - 加载状态视图

/// 加载颜色状态视图
struct LoadingColorView: View {
    @State private var rotationAngle: Double = 0
    
    var body: some View {
        VStack(spacing: 20) {
            // 旋转的调色板图标
            Image(systemName: "paintpalette.fill")
                .font(.system(size: 50))
                .foregroundStyle(
                    LinearGradient(
                        colors: [.indigo, .purple, .pink],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .rotationEffect(.degrees(rotationAngle))
                .onAppear {
                    withAnimation(.linear(duration: 2).repeatForever(autoreverses: false)) {
                        rotationAngle = 360
                    }
                }
            
            Text(LocalizedStringKey("extracting_colors"))
                .font(.title3)
                .foregroundColor(.primary)
            
            Text(LocalizedStringKey("please_wait"))
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            // 进度指示器
            ProgressView()
                .scaleEffect(1.2)
                .padding(.top, 8)
        }
        .padding(40)
    }
}

// MARK: - 错误状态视图

/// 错误颜色状态视图
struct ErrorColorView: View {
    let message: String
    let onRetry: () -> Void
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 50))
                .foregroundColor(.orange)
            
            Text(LocalizedStringKey("extraction_failed"))
                .font(.title3)
                .foregroundColor(.primary)
            
            Text(message)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Button(action: onRetry) {
                HStack(spacing: 8) {
                    Image(systemName: "arrow.clockwise")
                    Text(LocalizedStringKey("retry"))
                }
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white)
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(
                    Capsule()
                        .fill(
                            LinearGradient(
                                colors: [.indigo, .purple],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(40)
    }
}

// MARK: - 预览

#Preview("ColorGridView") {
    ScrollView {
        ColorGridView(colors: ExtractedColor.samples)
            .padding()
    }
}

#Preview("EmptyColorView") {
    EmptyColorView()
}

#Preview("LoadingColorView") {
    LoadingColorView()
}

#Preview("ErrorColorView") {
    ErrorColorView(message: "颜色提取失败，请重试") {
        print("重试")
    }
}