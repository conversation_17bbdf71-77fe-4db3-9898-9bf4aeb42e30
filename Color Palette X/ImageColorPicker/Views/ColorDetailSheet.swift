//
//  ColorDetailSheet.swift
//  Color Palette X
//
//  Created by <PERSON><PERSON> on 2025/7/27.
//

import SwiftUI

/// 颜色详情弹窗
/// 
/// 显示颜色的详细信息，包括不同格式的颜色值和操作选项
struct ColorDetailSheet: View {
    
    // MARK: - 属性
    
    let color: ExtractedColor
    
    // MARK: - 状态
    
    @Environment(\.dismiss) private var dismiss
    @State private var copiedFormat: ColorFormat?
    
    // MARK: - 颜色格式枚举
    
    enum ColorFormat: CaseIterable {
        case hex
        case rgb
        case hsl
        case hsb
        
        var title: LocalizedStringKey {
            switch self {
            case .hex: return LocalizedStringKey("hex_format")
            case .rgb: return LocalizedStringKey("rgb_format")
            case .hsl: return LocalizedStringKey("hsl_format")
            case .hsb: return LocalizedStringKey("hsb_format")
            }
        }
        
        var icon: String {
            switch self {
            case .hex: return "number"
            case .rgb: return "r.circle"
            case .hsl: return "l.circle"
            case .hsb: return "b.circle"
            }
        }
    }
    
    // MARK: - 视图
    
    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(spacing: 24) {
                    // 颜色预览区域
                    colorPreviewSection
                    
                    // 颜色格式列表
                    colorFormatsSection
                    
                    // 操作按钮
                    actionButtonsSection
                }
                .padding(20)
            }
            .navigationTitle(LocalizedStringKey("color_details"))
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(LocalizedStringKey("done")) {
                        dismiss()
                    }
                }
            }
        }
        .presentationDetents([.medium, .large])
        .presentationDragIndicator(.visible)
        .onAppear {
            AppLogger.info("显示颜色详情: \(color.hexString)", category: .ui)
        }
    }
    
    // MARK: - 子视图
    
    /// 颜色预览区域
    private var colorPreviewSection: some View {
        VStack(spacing: 16) {
            // 大颜色方块
            RoundedRectangle(cornerRadius: 20)
                .fill(color.color)
                .frame(height: 120)
                .shadow(color: color.color.opacity(0.4), radius: 12, x: 0, y: 6)
                .overlay(
                    VStack {
                        HStack {
                            Spacer()
                            Text(color.weightPercentage)
                                .font(.caption)
                                .fontWeight(.semibold)
                                .foregroundColor(ColorUtils.isDarkColor(color.color) ? .white : .black)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(
                                    Capsule()
                                        .fill(.ultraThinMaterial)
                                )
                        }
                        Spacer()
                    }
                    .padding(12)
                )
            
            // 主要信息
            VStack(spacing: 8) {
                Text(color.hexString)
                    .font(.title2)
                    .fontWeight(.bold)
                    .fontDesign(.monospaced)
                    .foregroundColor(.primary)
                
                Text("权重: \(color.weightPercentage)")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    /// 颜色格式列表
    private var colorFormatsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text(LocalizedStringKey("color_formats"))
                .font(.headline)
                .foregroundColor(.primary)
            
            VStack(spacing: 12) {
                ForEach(ColorFormat.allCases, id: \.self) { format in
                    ColorFormatRow(
                        format: format,
                        color: color,
                        isCopied: copiedFormat == format,
                        onCopy: {
                            copyColorFormat(format)
                        }
                    )
                }
            }
        }
    }
    
    /// 操作按钮区域
    private var actionButtonsSection: some View {
        VStack(spacing: 12) {
            // 添加到收藏
            Button(action: addToFavorites) {
                HStack {
                    Image(systemName: "heart")
                    Text(LocalizedStringKey("add_to_favorites"))
                }
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 14)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(
                            LinearGradient(
                                colors: [.pink, .red],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                )
            }
            .buttonStyle(PlainButtonStyle())
            
            // 分享颜色
            Button(action: shareColor) {
                HStack {
                    Image(systemName: "square.and.arrow.up")
                    Text(LocalizedStringKey("share_color"))
                }
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.primary)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 14)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(.systemGray6))
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
    }
    
    // MARK: - 方法
    
    /// 复制颜色格式
    /// - Parameter format: 颜色格式
    private func copyColorFormat(_ format: ColorFormat) {
        let colorValue = getColorValue(for: format)
        UIPasteboard.general.string = colorValue
        
        AppLogger.info("复制颜色格式 \(format): \(colorValue)", category: .ui)
        
        withAnimation(.easeInOut(duration: 0.3)) {
            copiedFormat = format
        }
        
        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
        
        // 清除复制状态
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            withAnimation(.easeInOut(duration: 0.3)) {
                copiedFormat = nil
            }
        }
    }
    
    /// 获取指定格式的颜色值
    /// - Parameter format: 颜色格式
    /// - Returns: 颜色值字符串
    private func getColorValue(for format: ColorFormat) -> String {
        let uiColor = UIColor(color.color)
        var red: CGFloat = 0
        var green: CGFloat = 0
        var blue: CGFloat = 0
        var alpha: CGFloat = 0
        
        uiColor.getRed(&red, green: &green, blue: &blue, alpha: &alpha)
        
        switch format {
        case .hex:
            return color.hexString
            
        case .rgb:
            let r = Int(red * 255)
            let g = Int(green * 255)
            let b = Int(blue * 255)
            return "rgb(\(r), \(g), \(b))"
            
        case .hsl:
            let hsl = rgbToHsl(red: red, green: green, blue: blue)
            return "hsl(\(Int(hsl.h)), \(Int(hsl.s * 100))%, \(Int(hsl.l * 100))%)"
            
        case .hsb:
            var hue: CGFloat = 0
            var saturation: CGFloat = 0
            var brightness: CGFloat = 0
            uiColor.getHue(&hue, saturation: &saturation, brightness: &brightness, alpha: &alpha)
            return "hsb(\(Int(hue * 360)), \(Int(saturation * 100))%, \(Int(brightness * 100))%)"
        }
    }
    
    /// RGB 转 HSL
    /// - Parameters:
    ///   - red: 红色分量
    ///   - green: 绿色分量
    ///   - blue: 蓝色分量
    /// - Returns: HSL 值
    private func rgbToHsl(red: CGFloat, green: CGFloat, blue: CGFloat) -> (h: CGFloat, s: CGFloat, l: CGFloat) {
        let max = Swift.max(red, green, blue)
        let min = Swift.min(red, green, blue)
        let delta = max - min
        
        let lightness = (max + min) / 2
        
        guard delta != 0 else {
            return (0, 0, lightness)
        }
        
        let saturation = lightness > 0.5 ? delta / (2 - max - min) : delta / (max + min)
        
        var hue: CGFloat = 0
        if max == red {
            hue = (green - blue) / delta + (green < blue ? 6 : 0)
        } else if max == green {
            hue = (blue - red) / delta + 2
        } else {
            hue = (red - green) / delta + 4
        }
        hue /= 6
        
        return (hue * 360, saturation, lightness)
    }
    
    /// 添加到收藏
    private func addToFavorites() {
        AppLogger.info("添加颜色到收藏: \(color.hexString)", category: .ui)
        // TODO: 实现添加到收藏的逻辑
    }
    
    /// 分享颜色
    private func shareColor() {
        AppLogger.info("分享颜色: \(color.hexString)", category: .ui)
        // TODO: 实现分享颜色的逻辑
    }
}

// MARK: - 颜色格式行

/// 颜色格式行组件
private struct ColorFormatRow: View {
    let format: ColorDetailSheet.ColorFormat
    let color: ExtractedColor
    let isCopied: Bool
    let onCopy: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            // 格式图标
            Image(systemName: format.icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.secondary)
                .frame(width: 24)
            
            // 格式标题
            Text(format.title)
                .font(.system(size: 15, weight: .medium))
                .foregroundColor(.primary)
                .frame(width: 60, alignment: .leading)
            
            // 颜色值
            Text(getColorValue(for: format))
                .font(.system(size: 14, design: .monospaced))
                .foregroundColor(.secondary)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            // 复制按钮
            Button(action: onCopy) {
                if isCopied {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 18))
                        .foregroundColor(.green)
                } else {
                    Image(systemName: "doc.on.doc")
                        .font(.system(size: 16))
                        .foregroundColor(.secondary)
                }
            }
            .buttonStyle(PlainButtonStyle())
            .frame(width: 32)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(Color(.systemGray6))
        )
        .animation(.easeInOut(duration: 0.2), value: isCopied)
    }
    
    /// 获取指定格式的颜色值
    /// - Parameter format: 颜色格式
    /// - Returns: 颜色值字符串
    private func getColorValue(for format: ColorDetailSheet.ColorFormat) -> String {
        let uiColor = UIColor(color.color)
        var red: CGFloat = 0
        var green: CGFloat = 0
        var blue: CGFloat = 0
        var alpha: CGFloat = 0
        
        uiColor.getRed(&red, green: &green, blue: &blue, alpha: &alpha)
        
        switch format {
        case .hex:
            return color.hexString
            
        case .rgb:
            let r = Int(red * 255)
            let g = Int(green * 255)
            let b = Int(blue * 255)
            return "rgb(\(r), \(g), \(b))"
            
        case .hsl:
            // 简化的 HSL 计算
            return "hsl(180, 50%, 50%)"
            
        case .hsb:
            var hue: CGFloat = 0
            var saturation: CGFloat = 0
            var brightness: CGFloat = 0
            uiColor.getHue(&hue, saturation: &saturation, brightness: &brightness, alpha: &alpha)
            return "hsb(\(Int(hue * 360)), \(Int(saturation * 100))%, \(Int(brightness * 100))%)"
        }
    }
}

// MARK: - 预览

#Preview("ColorDetailSheet") {
    ColorDetailSheet(color: ExtractedColor.samples[0])
}