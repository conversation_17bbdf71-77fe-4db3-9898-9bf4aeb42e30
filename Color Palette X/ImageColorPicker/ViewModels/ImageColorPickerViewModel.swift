//
//  ImageColorPickerViewModel.swift
//  Color Palette X
//
//  Created by <PERSON><PERSON> on 2025/7/27.
//

import SwiftUI
import UIKit
import PhotosUI

/// 图片取色主视图模型
/// 
/// 管理底部弹窗状态、导航路径和图片选择流程
@Observable
class ImageColorPickerViewModel {
    
    // MARK: - 状态属性
    
    /// 底部弹窗是否显示
    var isBottomSheetPresented = false
    
    /// 相机选择器是否显示
    var isCameraPresented = false
    
    /// 相册选择器是否显示
    var isPhotoPickerPresented = false
    
    /// 选中的图片
    var selectedImage: UIImage?
    
    /// 导航路径
    var navigationPath = NavigationPath()
    
    /// 是否正在处理图片选择
    var isProcessingSelection = false
    
    /// 错误消息
    var errorMessage: String?
    
    /// 是否显示错误提示
    var showErrorAlert = false
    
    // MARK: - 服务依赖
    
    private let cameraService: CameraService
    private let photoLibraryService: PhotoLibraryService
    
    // MARK: - 初始化
    
    init(
        cameraService: CameraService = .shared,
        photoLibraryService: PhotoLibraryService = .shared
    ) {
        self.cameraService = cameraService
        self.photoLibraryService = photoLibraryService
        
        AppLogger.info("ImageColorPickerViewModel 初始化", category: .ui)
    }
    
    // MARK: - 公共方法
    
    /// 显示图片来源选择弹窗
    func showImageSourceOptions() {
        AppLogger.info("显示图片来源选择弹窗", category: .ui)
        isBottomSheetPresented = true
    }
    
    /// 处理图片来源选择
    /// - Parameter source: 选择的图片来源
    func handleImageSourceSelection(_ source: ImageSource) {
        AppLogger.info("用户选择图片来源: \(source)", category: .ui)
        
        isBottomSheetPresented = false
        
        switch source {
        case .camera:
            handleCameraSelection()
        case .photoLibrary:
            handlePhotoLibrarySelection()
        case .cancel:
            AppLogger.debug("用户取消图片选择", category: .ui)
        }
    }
    
    /// 处理图片选择完成
    /// - Parameter image: 选中的图片
    func handleImageSelection(_ image: UIImage) {
        AppLogger.info("处理图片选择完成，图片尺寸: \(image.size)", category: .data)
        
        selectedImage = image
        navigateToColorExtraction()
    }
    
    /// 重置选择状态
    func resetSelection() {
        AppLogger.debug("重置图片选择状态", category: .ui)
        
        selectedImage = nil
        errorMessage = nil
        showErrorAlert = false
        isProcessingSelection = false
        navigationPath = NavigationPath()
    }
    
    /// 导航到颜色提取界面
    func navigateToColorExtraction() {
        guard selectedImage != nil else {
            AppLogger.warning("尝试导航到颜色提取界面但没有选中图片", category: .ui)
            return
        }
        
        AppLogger.info("导航到颜色提取界面", category: .ui)
        navigationPath.append("ColorExtraction")
    }
    
    /// 返回到主界面
    func navigateBack() {
        AppLogger.debug("返回到主界面", category: .ui)
        navigationPath.removeLast()
    }
    
    // MARK: - 私有方法
    
    /// 处理相机选择
    private func handleCameraSelection() {
        AppLogger.info("处理相机选择", category: .ui)
        
        Task {
            do {
                let hasPermission = await cameraService.requestCameraPermission()
                
                await MainActor.run {
                    if hasPermission && cameraService.isCameraAvailable() {
                        isCameraPresented = true
                    } else {
                        showError(CameraService.CameraError.permissionDenied.localizedDescription)
                    }
                }
            }
        }
    }
    
    /// 处理相册选择
    private func handlePhotoLibrarySelection() {
        AppLogger.info("处理相册选择", category: .ui)
        
        if photoLibraryService.isPhotoLibraryAvailable() {
            isPhotoPickerPresented = true
        } else {
            showError(PhotoLibraryService.PhotoLibraryError.unsupportedFormat.localizedDescription)
        }
    }
    
    /// 显示错误信息
    /// - Parameter message: 错误消息
    private func showError(_ message: String) {
        AppLogger.error("显示错误信息: \(message)", category: .ui)
        
        errorMessage = message
        showErrorAlert = true
    }
}

// MARK: - 相机处理

extension ImageColorPickerViewModel {
    /// 处理相机拍摄结果
    /// - Parameter info: 拍摄结果信息
    func handleCameraResult(_ info: [UIImagePickerController.InfoKey: Any]) {
        AppLogger.info("处理相机拍摄结果", category: .data)
        
        isProcessingSelection = true
        
        Task {
            do {
                let image = try cameraService.handleCameraResult(info)
                
                await MainActor.run {
                    isProcessingSelection = false
                    handleImageSelection(image)
                }
            } catch {
                await MainActor.run {
                    isProcessingSelection = false
                    showError(error.localizedDescription)
                }
            }
        }
    }
    
    /// 处理相机取消
    func handleCameraCancel() {
        AppLogger.debug("用户取消相机拍摄", category: .ui)
        isProcessingSelection = false
    }
}

// MARK: - 相册处理

extension ImageColorPickerViewModel {
    /// 处理相册选择结果
    /// - Parameter results: 选择结果
    func handlePhotoPickerResult(_ results: [PHPickerResult]) {
        AppLogger.info("处理相册选择结果", category: .data)
        
        isProcessingSelection = true
        
        Task {
            do {
                let image = try await photoLibraryService.handlePhotoSelection(results)
                let processedImage = photoLibraryService.processSelectedImage(image)
                
                await MainActor.run {
                    isProcessingSelection = false
                    handleImageSelection(processedImage)
                }
            } catch {
                await MainActor.run {
                    isProcessingSelection = false
                    showError(error.localizedDescription)
                }
            }
        }
    }
    
    /// 处理相册选择取消
    func handlePhotoPickerCancel() {
        AppLogger.debug("用户取消相册选择", category: .ui)
        isProcessingSelection = false
    }
}

// MARK: - 预览和测试支持

#if DEBUG
extension ImageColorPickerViewModel {
    /// 创建测试用的视图模型
    static func createMockViewModel() -> ImageColorPickerViewModel {
        return ImageColorPickerViewModel(
            cameraService: .createMockService(),
            photoLibraryService: .createMockService()
        )
    }
    
    /// 设置模拟选中的图片
    func setMockSelectedImage() {
        selectedImage = UIImage.createTestColorImage()
        AppLogger.debug("设置模拟选中图片", category: .ui)
    }
}
#endif