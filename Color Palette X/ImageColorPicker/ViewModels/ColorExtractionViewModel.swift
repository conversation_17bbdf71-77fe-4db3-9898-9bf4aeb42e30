//
//  ColorExtractionViewModel.swift
//  Color Palette X
//
//  Created by <PERSON><PERSON> on 2025/7/27.
//

import SwiftUI
import UIKit

/// 颜色提取视图模型
/// 
/// 管理颜色提取状态、加载状态和异步颜色提取操作
@Observable
class ColorExtractionViewModel {
    
    // MARK: - 状态属性
    
    /// 当前图片
    var image: UIImage?
    
    /// 提取状态
    var extractionState: ColorExtractionState = .idle
    
    /// 提取参数
    var extractionCount = 8
    var extractionPasses = 5
    var usePerceptualSpace = true
    
    /// 是否显示参数设置
    var showParameterSettings = false
    
    // MARK: - 计算属性
    
    /// 是否正在加载
    var isLoading: Bool {
        extractionState.isLoading
    }
    
    /// 是否有错误
    var hasError: Bool {
        extractionState.hasError
    }
    
    /// 错误消息
    var errorMessage: String? {
        extractionState.errorMessage
    }
    
    /// 提取的颜色
    var extractedColors: [ExtractedColor] {
        extractionState.extractedColors
    }
    
    /// 是否有结果
    var hasResults: Bool {
        extractionState.hasResults
    }
    
    // MARK: - 服务依赖
    
    private let colorExtractionService: ColorExtractionService
    
    // MARK: - 初始化
    
    init(
        image: UIImage? = nil,
        colorExtractionService: ColorExtractionService = .shared
    ) {
        self.image = image
        self.colorExtractionService = colorExtractionService
        
        AppLogger.info("ColorExtractionViewModel 初始化", category: .ui)
        
        // 如果有图片，自动开始提取
        if image != nil {
            Task {
                await extractColors()
            }
        }
    }
    
    // MARK: - 公共方法
    
    /// 设置图片并开始提取颜色
    /// - Parameter newImage: 新图片
    func setImage(_ newImage: UIImage) {
        AppLogger.info("设置新图片并开始颜色提取", category: .data)
        
        image = newImage
        Task {
            await extractColors()
        }
    }
    
    /// 异步提取颜色
    func extractColors() async {
        guard let image = image else {
            AppLogger.warning("尝试提取颜色但没有图片", category: .data)
            await MainActor.run {
                extractionState = .error(NSLocalizedString("no_image_provided", comment: "未提供图片"))
            }
            return
        }
        
        AppLogger.info("开始异步颜色提取", category: .performance)
        
        await MainActor.run {
            extractionState = .loading
        }
        
        do {
            let colors = try await colorExtractionService.extractDominantColors(
                from: image,
                count: extractionCount,
                passes: extractionPasses,
                perceptual: usePerceptualSpace
            )
            
            await MainActor.run {
                extractionState = .success(colors)
                AppLogger.info("颜色提取成功，提取了 \(colors.count) 个颜色", category: .performance)
            }
            
        } catch {
            await MainActor.run {
                extractionState = .error(error.localizedDescription)
                AppLogger.error("颜色提取失败: \(error.localizedDescription)", category: .data)
            }
        }
    }
    
    /// 重试颜色提取
    func retryExtraction() async {
        AppLogger.info("重试颜色提取", category: .ui)
        await extractColors()
    }
    
    /// 取消当前提取任务
    @MainActor func cancelExtraction() {
        AppLogger.info("取消颜色提取任务", category: .ui)
        colorExtractionService.cancelCurrentTask()
        extractionState = .idle
    }
    
    /// 清理资源
    @MainActor func cleanup() {
        AppLogger.debug("清理颜色提取资源", category: .performance)
        cancelExtraction()
        colorExtractionService.clearCache()
    }
    
    // MARK: - 参数设置
    
    /// 更新提取参数
    /// - Parameters:
    ///   - count: 颜色数量
    ///   - passes: 迭代次数
    ///   - perceptual: 是否使用感知色彩空间
    func updateExtractionParameters(count: Int, passes: Int, perceptual: Bool) {
        AppLogger.info("更新提取参数 - count: \(count), passes: \(passes), perceptual: \(perceptual)", category: .ui)
        
        extractionCount = count
        extractionPasses = passes
        usePerceptualSpace = perceptual
        
        // 如果有图片，重新提取
        if image != nil {
            Task {
                await extractColors()
            }
        }
    }
    
    /// 重置参数到默认值
    func resetParametersToDefault() {
        AppLogger.debug("重置提取参数到默认值", category: .ui)
        updateExtractionParameters(count: 8, passes: 5, perceptual: true)
    }
    
    /// 显示/隐藏参数设置
    func toggleParameterSettings() {
        showParameterSettings.toggle()
        AppLogger.debug("切换参数设置显示状态: \(showParameterSettings)", category: .ui)
    }
}

// MARK: - 颜色操作

extension ColorExtractionViewModel {
    /// 复制颜色十六进制值到剪贴板
    /// - Parameter color: 要复制的颜色
    func copyColorToClipboard(_ color: ExtractedColor) {
        UIPasteboard.general.string = color.hexString
        AppLogger.info("复制颜色到剪贴板: \(color.hexString)", category: .ui)
    }
    
    /// 获取颜色的详细信息
    /// - Parameter color: 颜色对象
    /// - Returns: 颜色详细信息字符串
    func getColorDetails(_ color: ExtractedColor) -> String {
        return """
        十六进制: \(color.hexString)
        权重: \(color.weightPercentage)
        """
    }
    
    /// 按权重过滤颜色
    /// - Parameter minimumWeight: 最小权重阈值
    /// - Returns: 过滤后的颜色数组
    func getFilteredColors(minimumWeight: Double = 0.1) -> [ExtractedColor] {
        return extractedColors.filter { $0.weight >= minimumWeight }
    }
}

// MARK: - 导出功能

extension ColorExtractionViewModel {
    /// 导出颜色调色板
    /// - Returns: 调色板数据
    func exportColorPalette() -> [String: Any] {
        let paletteData: [String: Any] = [
            "colors": extractedColors.map { color in
                [
                    "hex": color.hexString,
                    "weight": color.weight
                ]
            },
            "extractionParameters": [
                "count": extractionCount,
                "passes": extractionPasses,
                "perceptual": usePerceptualSpace
            ],
            "timestamp": Date().timeIntervalSince1970
        ]
        
        AppLogger.info("导出颜色调色板，包含 \(extractedColors.count) 个颜色", category: .data)
        return paletteData
    }
}

// MARK: - 预览和测试支持

#if DEBUG
extension ColorExtractionViewModel {
    /// 创建测试用的视图模型
    @MainActor static func createMockViewModel() -> ColorExtractionViewModel {
        let mockImage = UIImage.createTestColorImage()
        return ColorExtractionViewModel(
            image: mockImage,
            colorExtractionService: .createMockService()
        )
    }
    
    /// 设置模拟提取状态
    func setMockExtractionState(_ state: ColorExtractionState) {
        extractionState = state
        AppLogger.debug("设置模拟提取状态: \(state)", category: .ui)
    }
    
    /// 模拟颜色提取
    func mockExtractColors() async {
        AppLogger.debug("模拟颜色提取", category: .ui)
        
        extractionState = .loading
        
        // 模拟处理时间
        try? await Task.sleep(nanoseconds: 2_000_000_000) // 2秒
        
        extractionState = .success(ExtractedColor.samples)
    }
}
#endif
