//
//  PhotoLibraryService.swift
//  Color Palette X
//
//  Created by <PERSON><PERSON> on 2025/7/27.
//

import UIKit
import PhotosUI

/// 相册服务
/// 
/// 处理相册图片选择功能（iOS 14+ 使用 PHPickerViewController，无需权限）
class PhotoLibraryService {
    
    // MARK: - 错误类型
    
    enum PhotoLibraryError: Error, LocalizedError {
        case selectionCancelled
        case imageLoadFailed
        case unsupportedFormat
        case noImageSelected
        
        var errorDescription: String? {
            switch self {
            case .selectionCancelled:
                return NSLocalizedString("photo_selection_cancelled", comment: "图片选择已取消")
            case .imageLoadFailed:
                return NSLocalizedString("image_load_failed", comment: "图片加载失败")
            case .unsupportedFormat:
                return NSLocalizedString("unsupported_image_format", comment: "不支持的图片格式")
            case .noImageSelected:
                return NSLocalizedString("no_image_selected", comment: "未选择图片")
            }
        }
    }
    
    // MARK: - 相册选择器
    
    /// 创建相册选择器
    /// - Returns: 配置好的 PHPickerViewController
    func createPhotoPickerController() -> PHPickerViewController {
        AppLogger.info("创建相册选择器", category: .ui)
        
        var configuration = PHPickerConfiguration()
        configuration.filter = .images
        configuration.selectionLimit = 1
        configuration.preferredAssetRepresentationMode = .current
        
        let picker = PHPickerViewController(configuration: configuration)
        
        AppLogger.debug("相册选择器创建成功", category: .ui)
        return picker
    }
    
    /// 处理相册选择结果
    /// - Parameter results: 选择结果
    /// - Returns: 选中的图片
    /// - Throws: PhotoLibraryError 如果处理失败
    func handlePhotoSelection(_ results: [PHPickerResult]) async throws -> UIImage {
        AppLogger.info("处理相册选择结果", category: .data)
        
        guard let result = results.first else {
            AppLogger.warning("未选择任何图片", category: .data)
            throw PhotoLibraryError.noImageSelected
        }
        
        guard result.itemProvider.canLoadObject(ofClass: UIImage.self) else {
            AppLogger.error("选中的项目不是有效的图片", category: .data)
            throw PhotoLibraryError.unsupportedFormat
        }
        
        return try await withCheckedThrowingContinuation { continuation in
            result.itemProvider.loadObject(ofClass: UIImage.self) { object, error in
                if let error = error {
                    AppLogger.error("图片加载失败: \(error.localizedDescription)", category: .data)
                    continuation.resume(throwing: PhotoLibraryError.imageLoadFailed)
                    return
                }
                
                guard let image = object as? UIImage else {
                    AppLogger.error("无法将对象转换为 UIImage", category: .data)
                    continuation.resume(throwing: PhotoLibraryError.imageLoadFailed)
                    return
                }
                
                AppLogger.info("成功加载选中图片，尺寸: \(image.size)", category: .data)
                continuation.resume(returning: image)
            }
        }
    }
    
    /// 检查是否支持相册选择
    /// - Returns: 是否支持
    func isPhotoLibraryAvailable() -> Bool {
       let available = PHPickerViewController.isAvailable
       AppLogger.debug("相册选择器可用性: \(available)", category: .general)
        return availableavailable
    }
}

// MARK: - 图片处理辅助方法

extension PhotoLibraryService {
    /// 处理图片方向和尺寸
    /// - Parameter image: 原始图片
    /// - Returns: 处理后的图片
    func processSelectedImage(_ image: UIImage) -> UIImage {
        AppLogger.debug("处理选中的图片", category: .data)
        
        // 修正图片方向
        let orientationFixedImage = fixImageOrientation(image)
        
        // 如果图片过大，进行适当缩放
        let processedImage = resizeImageIfNeeded(orientationFixedImage)
        
        AppLogger.debug("图片处理完成，最终尺寸: \(processedImage.size)", category: .data)
        return processedImage
    }
    
    /// 修正图片方向
    /// - Parameter image: 原始图片
    /// - Returns: 方向修正后的图片
    private func fixImageOrientation(_ image: UIImage) -> UIImage {
        if image.imageOrientation == .up {
            return image
        }
        
        UIGraphicsBeginImageContextWithOptions(image.size, false, image.scale)
        defer { UIGraphicsEndImageContext() }
        
        image.draw(in: CGRect(origin: .zero, size: image.size))
        return UIGraphicsGetImageFromCurrentImageContext() ?? image
    }
    
    /// 如果需要，调整图片尺寸
    /// - Parameter image: 原始图片
    /// - Returns: 调整后的图片
    private func resizeImageIfNeeded(_ image: UIImage) -> UIImage {
        let maxDimension: CGFloat = 2048
        
        if image.size.width <= maxDimension && image.size.height <= maxDimension {
            return image
        }
        
        let scale = min(maxDimension / image.size.width, maxDimension / image.size.height)
        let newSize = CGSize(
            width: image.size.width * scale,
            height: image.size.height * scale
        )
        
        AppLogger.debug("图片尺寸过大，缩放至: \(newSize)", category: .performance)
        
        UIGraphicsBeginImageContextWithOptions(newSize, false, image.scale)
        defer { UIGraphicsEndImageContext() }
        
        image.draw(in: CGRect(origin: .zero, size: newSize))
        return UIGraphicsGetImageFromCurrentImageContext() ?? image
    }
}

// MARK: - 单例支持

extension PhotoLibraryService {
    /// 共享实例
    static let shared = PhotoLibraryService()
}

// MARK: - 预览和测试支持

#if DEBUG
extension PhotoLibraryService {
    /// 创建模拟相册服务（用于测试）
    static func createMockService() -> PhotoLibraryService {
        return PhotoLibraryService()
    }
    
    /// 模拟选择图片
    func mockSelectImage() async throws -> UIImage {
        AppLogger.debug("模拟选择图片", category: .data)
        
        // 模拟选择延迟
        try await Task.sleep(nanoseconds: 1_000_000_000) // 1秒
        
        guard let mockImage = UIImage.createTestColorImage(size: CGSize(width: 600, height: 400)) else {
            throw PhotoLibraryError.imageLoadFailed
        }
        
        return mockImage
    }
    
    /// 创建模拟的 PHPickerResult
    func createMockPickerResults() -> [PHPickerResult] {
        // 在实际测试中，这里需要创建模拟的 PHPickerResult
        // 由于 PHPickerResult 的复杂性，这里返回空数组
        return []
    }
}
#endif
