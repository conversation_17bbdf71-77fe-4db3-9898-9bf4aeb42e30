//
//  ColorExtractionService.swift
//  Color Palette X
//
//  Created by <PERSON><PERSON> on 2025/7/27.
//

import UIKit
import Foundation

/// 颜色提取服务
/// 
/// 提供异步颜色提取功能，包括取消机制、超时处理和错误重试
@MainActor
class ColorExtractionService {
    
    // MARK: - 属性
    
    /// 当前提取任务
    private var currentTask: Task<[ExtractedColor], Error>?
    
    /// 提取超时时间（秒）
    private let timeoutInterval: TimeInterval = 30.0
    
    /// 缓存提取结果
    private var cache: [String: [ExtractedColor]] = [:]
    
    // MARK: - 公共方法
    
    /// 异步提取图片中的主要颜色
    /// - Parameters:
    ///   - image: 要处理的图片
    ///   - count: 提取的颜色数量
    ///   - passes: K-Means 迭代次数
    ///   - perceptual: 是否使用感知色彩空间
    /// - Returns: 提取的颜色数组
    /// - Throws: 提取过程中的错误
    func extractDominantColors(
        from image: UIImage,
        count: Int = 8,
        passes: Int = 5,
        perceptual: Bool = true
    ) async throws -> [ExtractedColor] {
        
        AppLogger.info("开始异步颜色提取服务", category: .performance)
        
        // 取消之前的任务
        cancelCurrentTask()
        
        // 检查缓存
        let cacheKey = generateCacheKey(image: image, count: count, passes: passes, perceptual: perceptual)
        if let cachedColors = cache[cacheKey] {
            AppLogger.debug("使用缓存的颜色提取结果", category: .performance)
            return cachedColors
        }
        
        // 创建新的提取任务
        currentTask = Task {
            return try await performExtraction(
                image: image,
                count: count,
                passes: passes,
                perceptual: perceptual
            )
        }
        
        // 执行任务并处理超时
        let colors = try await withTimeout(timeoutInterval) {
            try await currentTask!.value
        }
        
        // 缓存结果
        cache[cacheKey] = colors
        
        // 清理缓存（保持最近的 10 个结果）
        if cache.count > 10 {
            let oldestKey = cache.keys.first!
            cache.removeValue(forKey: oldestKey)
        }
        
        AppLogger.info("颜色提取服务完成，提取了 \(colors.count) 个颜色", category: .performance)
        
        return colors
    }
    
    /// 取消当前的提取任务
    func cancelCurrentTask() {
        currentTask?.cancel()
        currentTask = nil
        AppLogger.debug("取消当前颜色提取任务", category: .performance)
    }
    
    /// 清理缓存
    func clearCache() {
        cache.removeAll()
        AppLogger.debug("清理颜色提取缓存", category: .performance)
    }
    
    // MARK: - 私有方法
    
    /// 执行实际的颜色提取
    /// - Parameters:
    ///   - image: 图片
    ///   - count: 颜色数量
    ///   - passes: 迭代次数
    ///   - perceptual: 感知色彩空间
    /// - Returns: 提取的颜色
    /// - Throws: 提取错误
    private func performExtraction(
        image: UIImage,
        count: Int,
        passes: Int,
        perceptual: Bool
    ) async throws -> [ExtractedColor] {
        
        return try await withCheckedThrowingContinuation { continuation in
            // 在后台队列执行颜色提取
            Task.detached(priority: .userInitiated) {
                do {
                    let colors = try image.extractDominantColors(
                        count: count,
                        passes: passes,
                        perceptual: perceptual
                    )
                    continuation.resume(returning: colors)
                } catch {
                    AppLogger.error("颜色提取失败: \(error.localizedDescription)", category: .data)
                    continuation.resume(throwing: error)
                }
            }
        }
    }
    
    /// 生成缓存键
    /// - Parameters:
    ///   - image: 图片
    ///   - count: 颜色数量
    ///   - passes: 迭代次数
    ///   - perceptual: 感知色彩空间
    /// - Returns: 缓存键字符串
    private func generateCacheKey(
        image: UIImage,
        count: Int,
        passes: Int,
        perceptual: Bool
    ) -> String {
        // 使用图片的哈希值和参数生成缓存键
        let imageHash = image.hashValue
        return "\(imageHash)_\(count)_\(passes)_\(perceptual)"
    }
    
    /// 带超时的异步操作
    /// - Parameters:
    ///   - timeout: 超时时间
    ///   - operation: 要执行的操作
    /// - Returns: 操作结果
    /// - Throws: 超时或操作错误
    private func withTimeout<T>(
        _ timeout: TimeInterval,
        operation: @escaping () async throws -> T
    ) async throws -> T {
        
        return try await withThrowingTaskGroup(of: T.self) { group in
            // 添加主要操作
            group.addTask {
                try await operation()
            }
            
            // 添加超时任务
            group.addTask {
                try await Task.sleep(nanoseconds: UInt64(timeout * 1_000_000_000))
                throw ColorExtractionServiceError.timeout
            }
            
            // 返回第一个完成的结果
            let result = try await group.next()!
            group.cancelAll()
            return result
        }
    }
}

// MARK: - 错误类型

/// 颜色提取服务错误
enum ColorExtractionServiceError: Error, LocalizedError {
    case timeout
    case cancelled
    case memoryWarning
    
    var errorDescription: String? {
        switch self {
        case .timeout:
            return NSLocalizedString("extraction_timeout", comment: "颜色提取超时")
        case .cancelled:
            return NSLocalizedString("extraction_cancelled", comment: "颜色提取已取消")
        case .memoryWarning:
            return NSLocalizedString("memory_warning", comment: "内存不足警告")
        }
    }
}

// MARK: - 单例支持

extension ColorExtractionService {
    /// 共享实例
    static let shared = ColorExtractionService()
}

// MARK: - 预览和测试支持

#if DEBUG
extension ColorExtractionService {
    /// 创建测试用的模拟服务
    static func createMockService() -> ColorExtractionService {
        return ColorExtractionService()
    }
    
    /// 模拟提取颜色（用于测试）
    func mockExtractColors() async throws -> [ExtractedColor] {
        // 模拟处理时间
        try await Task.sleep(nanoseconds: 1_000_000_000) // 1秒
        
        // 返回模拟颜色
        return ExtractedColor.samples
    }
}
#endif