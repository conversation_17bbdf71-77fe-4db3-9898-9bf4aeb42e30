//
//  CameraService.swift
//  Color Palette X
//
//  Created by <PERSON><PERSON> on 2025/7/27.
//

import UIKit
import AVFoundation

/// 相机服务
/// 
/// 处理相机权限请求和拍照功能
class CameraService {
    
    // MARK: - 错误类型
    
    enum CameraError: Error, LocalizedError {
        case permissionDenied
        case cameraNotAvailable
        case captureSessionFailed
        case imageCaptureFailed
        
        var errorDescription: String? {
            switch self {
            case .permissionDenied:
                return NSLocalizedString("camera_permission_denied", comment: "相机权限被拒绝")
            case .cameraNotAvailable:
                return NSLocalizedString("camera_not_available", comment: "相机不可用")
            case .captureSessionFailed:
                return NSLocalizedString("capture_session_failed", comment: "相机会话失败")
            case .imageCaptureFailed:
                return NSLocalizedString("image_capture_failed", comment: "图片拍摄失败")
            }
        }
    }
    
    // MARK: - 权限管理
    
    /// 请求相机权限
    /// - Returns: 是否获得权限
    func requestCameraPermission() async -> Bool {
        AppLogger.info("请求相机权限", category: .auth)
        
        let status = AVCaptureDevice.authorizationStatus(for: .video)
        
        switch status {
        case .authorized:
            AppLogger.info("相机权限已授权", category: .auth)
            return true
            
        case .notDetermined:
            AppLogger.info("相机权限未确定，请求授权", category: .auth)
            let granted = await AVCaptureDevice.requestAccess(for: .video)
            AppLogger.info("相机权限请求结果: \(granted)", category: .auth)
            return granted
            
        case .denied, .restricted:
            AppLogger.warning("相机权限被拒绝或受限", category: .auth)
            return false
            
        @unknown default:
            AppLogger.warning("未知的相机权限状态", category: .auth)
            return false
        }
    }
    
    /// 检查相机是否可用
    /// - Returns: 相机是否可用
    func isCameraAvailable() -> Bool {
        let available = UIImagePickerController.isSourceTypeAvailable(.camera)
        AppLogger.debug("相机可用性: \(available)", category: .general)
        return available
    }
    
    // MARK: - 相机控制器
    
    /// 创建相机控制器
    /// - Returns: 配置好的 UIImagePickerController
    /// - Throws: CameraError 如果相机不可用
    func createCameraController() throws -> UIImagePickerController {
        AppLogger.info("创建相机控制器", category: .ui)
        
        guard isCameraAvailable() else {
            AppLogger.error("相机不可用", category: .general)
            throw CameraError.cameraNotAvailable
        }
        
        let picker = UIImagePickerController()
        picker.sourceType = .camera
        picker.mediaTypes = ["public.image"]
        picker.allowsEditing = false
        picker.cameraDevice = .rear
        picker.cameraFlashMode = .auto
        
        // 设置相机界面
        if let cameraOverlayView = createCameraOverlayView() {
            picker.cameraOverlayView = cameraOverlayView
        }
        
        AppLogger.debug("相机控制器创建成功", category: .ui)
        return picker
    }
    
    /// 处理相机拍摄结果
    /// - Parameter info: 拍摄结果信息
    /// - Returns: 拍摄的图片
    /// - Throws: CameraError 如果图片获取失败
    func handleCameraResult(_ info: [UIImagePickerController.InfoKey: Any]) throws -> UIImage {
        AppLogger.info("处理相机拍摄结果", category: .data)
        
        guard let image = info[.originalImage] as? UIImage else {
            AppLogger.error("无法从相机结果中获取图片", category: .data)
            throw CameraError.imageCaptureFailed
        }
        
        AppLogger.info("成功获取拍摄图片，尺寸: \(image.size)", category: .data)
        return image
    }
    
    // MARK: - 私有方法
    
    /// 创建相机覆盖视图（可选的自定义界面）
    /// - Returns: 覆盖视图
    private func createCameraOverlayView() -> UIView? {
        // 这里可以添加自定义的相机界面元素
        // 比如网格线、取色提示等
        return nil
    }
}

// MARK: - 权限设置引导

extension CameraService {
    /// 显示权限设置引导
    /// - Parameter viewController: 当前视图控制器
    func showPermissionSettingsAlert(from viewController: UIViewController) {
        AppLogger.info("显示相机权限设置引导", category: .ui)
        
        let alert = UIAlertController(
            title: NSLocalizedString("camera_permission_required", comment: "需要相机权限"),
            message: NSLocalizedString("camera_permission_message", comment: "请在设置中允许访问相机以使用拍照功能"),
            preferredStyle: .alert
        )
        
        // 设置按钮
        alert.addAction(UIAlertAction(
            title: NSLocalizedString("settings", comment: "设置"),
            style: .default
        ) { _ in
            if let settingsURL = URL(string: UIApplication.openSettingsURLString) {
                UIApplication.shared.open(settingsURL)
            }
        })
        
        // 取消按钮
        alert.addAction(UIAlertAction(
            title: NSLocalizedString("cancel", comment: "取消"),
            style: .cancel
        ))
        
        viewController.present(alert, animated: true)
    }
}

// MARK: - 单例支持

extension CameraService {
    /// 共享实例
    static let shared = CameraService()
}

// MARK: - 预览和测试支持

#if DEBUG
extension CameraService {
    /// 创建模拟相机服务（用于测试）
    static func createMockService() -> CameraService {
        return CameraService()
    }
    
    /// 模拟相机权限授权
    func mockGrantPermission() async -> Bool {
        AppLogger.debug("模拟相机权限授权", category: .auth)
        // 模拟权限请求延迟
        try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
        return true
    }
    
    /// 创建测试用的图片
    func createMockCapturedImage() -> UIImage? {
        return UIImage.createTestColorImage(size: CGSize(width: 400, height: 400))
    }
}
#endif