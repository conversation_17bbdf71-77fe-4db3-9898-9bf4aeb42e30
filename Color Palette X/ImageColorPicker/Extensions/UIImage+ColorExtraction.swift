//
//  UIImage+ColorExtraction.swift
//  Color Palette X
//
//  Created by <PERSON><PERSON> on 2025/7/27.
//

import CoreImage
import UIKit

/// UIImage 颜色提取扩展
/// 
/// 提供从图片中提取主要颜色的功能，使用 K-Means 聚类算法
extension UIImage {
    
    /// 颜色提取错误类型
    enum ColorExtractionError: Error, LocalizedError {
        case invalidImage
        case ciImageCreationFailed
        case filterFailed
        case cgImageCreationFailed
        case dataExtractionFailed
        case noColorsExtracted
        
        var errorDescription: String? {
            switch self {
            case .invalidImage:
                return NSLocalizedString("invalid_image", comment: "无效的图片")
            case .ciImageCreationFailed:
                return NSLocalizedString("ci_image_creation_failed", comment: "CIImage 创建失败")
            case .filterFailed:
                return NSLocalizedString("filter_failed", comment: "滤镜处理失败")
            case .cgImageCreationFailed:
                return NSLocalizedString("cg_image_creation_failed", comment: "CGImage 创建失败")
            case .dataExtractionFailed:
                return NSLocalizedString("data_extraction_failed", comment: "数据提取失败")
            case .noColorsExtracted:
                return NSLocalizedString("no_colors_extracted", comment: "未提取到颜色")
            }
        }
    }
    
    /// 从图片中提取指定数量的主颜色
    /// - Parameters:
    ///   - count: 要提取的主颜色数量 (默认值为 8)
    ///   - passes: K-Means 算法的迭代次数 (默认值为 5)
    ///   - perceptual: 是否使用感知色彩空间进行计算 (默认值为 true)
    /// - Returns: 一个包含 ExtractedColor 对象数组，按其在图像中的权重降序排列
    /// - Throws: ColorExtractionError 如果提取过程失败
    func extractDominantColors(
        count: Int = 8,
        passes: Int = 5,
        perceptual: Bool = true
    ) throws -> [ExtractedColor] {
        
        AppLogger.info("开始提取颜色 - count: \(count), passes: \(passes), perceptual: \(perceptual)", category: .performance)
        
        // 验证参数
        guard count > 0 && count <= 32 else {
            throw ColorExtractionError.invalidImage
        }
        
        guard passes > 0 && passes <= 20 else {
            throw ColorExtractionError.invalidImage
        }
        
        // 预处理图片
        let processedImage = preprocessImage()
        
        // 创建 CIImage
        guard let ciImage = CIImage(image: processedImage) else {
            AppLogger.error("CIImage 创建失败", category: .data)
            throw ColorExtractionError.ciImageCreationFailed
        }
        
        // 执行 K-Means 聚类
        let outputCIImage = try performKMeansExtraction(
            ciImage: ciImage,
            count: count,
            passes: passes,
            perceptual: perceptual
        )
        
        // 解析颜色结果
        let extractedColors = try parseColorResults(from: outputCIImage)
        
        AppLogger.info("成功提取 \(extractedColors.count) 个颜色", category: .performance)
        
        return extractedColors
    }
    
    // MARK: - 私有方法
    
    /// 预处理图片以优化性能
    /// - Returns: 处理后的图片
    private func preprocessImage() -> UIImage {
        let maxDimension: CGFloat = 800
        
        // 如果图片尺寸过大，进行缩放
        if size.width > maxDimension || size.height > maxDimension {
            let scale = min(maxDimension / size.width, maxDimension / size.height)
            let newSize = CGSize(width: size.width * scale, height: size.height * scale)
            
            AppLogger.debug("图片尺寸过大，缩放至 \(newSize)", category: .performance)
            
            return resized(to: newSize) ?? self
        }
        
        return self
    }
    
    /// 执行简化的颜色提取算法
    /// - Parameters:
    ///   - ciImage: 输入的 CIImage
    ///   - count: 颜色数量
    ///   - passes: 迭代次数（此实现中未使用）
    ///   - perceptual: 是否使用感知色彩空间（此实现中未使用）
    /// - Returns: 包含主要颜色的 CIImage
    /// - Throws: ColorExtractionError
    private func performKMeansExtraction(
        ciImage: CIImage,
        count: Int,
        passes: Int,
        perceptual: Bool
    ) throws -> CIImage {
        
        // 使用简化的颜色提取算法
        let context = CIContext(options: nil)
        
        guard let cgImage = context.createCGImage(ciImage, from: ciImage.extent) else {
            AppLogger.error("CGImage 创建失败", category: .data)
            throw ColorExtractionError.cgImageCreationFailed
        }
        
        // 提取主要颜色
        let dominantColors = try extractDominantColorsFromCGImage(cgImage, count: count)
        
        // 创建包含颜色结果的图片
        let resultImage = try createColorResultImage(from: dominantColors)
        
        guard let resultCIImage = CIImage(image: resultImage) else {
            AppLogger.error("结果 CIImage 创建失败", category: .data)
            throw ColorExtractionError.ciImageCreationFailed
        }
        
        return resultCIImage
    }
    
    /// 从 CGImage 中提取主要颜色
    /// - Parameters:
    ///   - cgImage: 输入的 CGImage
    ///   - count: 要提取的颜色数量
    /// - Returns: 主要颜色数组
    /// - Throws: ColorExtractionError
    private func extractDominantColorsFromCGImage(_ cgImage: CGImage, count: Int) throws -> [UIColor] {
        let width = cgImage.width
        let height = cgImage.height
        
        guard let data = cgImage.dataProvider?.data else {
            throw ColorExtractionError.dataExtractionFailed
        }
        
        let pointer: UnsafePointer<UInt8> = CFDataGetBytePtr(data)
        var colorCounts: [UIColor: Int] = [:]
        
        // 采样像素（每隔一定间距采样以提高性能）
        let sampleStep = max(1, min(width, height) / 100)
        
        for y in stride(from: 0, to: height, by: sampleStep) {
            for x in stride(from: 0, to: width, by: sampleStep) {
                let offset = (y * width + x) * 4
                
                let red = CGFloat(pointer[offset]) / 255.0
                let green = CGFloat(pointer[offset + 1]) / 255.0
                let blue = CGFloat(pointer[offset + 2]) / 255.0
                
                // 量化颜色以减少颜色数量
                let quantizedColor = quantizeColor(red: red, green: green, blue: blue)
                colorCounts[quantizedColor, default: 0] += 1
            }
        }
        
        // 按出现次数排序并取前N个
        let sortedColors = colorCounts.sorted { $0.value > $1.value }
        let dominantColors = Array(sortedColors.prefix(count)).map { $0.key }
        
        return dominantColors
    }
    
    /// 量化颜色以减少颜色变化
    /// - Parameters:
    ///   - red: 红色分量
    ///   - green: 绿色分量
    ///   - blue: 蓝色分量
    /// - Returns: 量化后的颜色
    private func quantizeColor(red: CGFloat, green: CGFloat, blue: CGFloat) -> UIColor {
        let quantizationLevel: CGFloat = 32.0 // 量化级别
        
        let quantizedRed = round(red * quantizationLevel) / quantizationLevel
        let quantizedGreen = round(green * quantizationLevel) / quantizationLevel
        let quantizedBlue = round(blue * quantizationLevel) / quantizationLevel
        
        return UIColor(red: quantizedRed, green: quantizedGreen, blue: quantizedBlue, alpha: 1.0)
    }
    
    /// 创建包含颜色结果的图片
    /// - Parameter colors: 主要颜色数组
    /// - Returns: 包含颜色信息的图片
    /// - Throws: ColorExtractionError
    private func createColorResultImage(from colors: [UIColor]) throws -> UIImage {
        let imageSize = CGSize(width: colors.count, height: 1)
        
        UIGraphicsBeginImageContextWithOptions(imageSize, false, 1.0)
        defer { UIGraphicsEndImageContext() }
        
        guard let context = UIGraphicsGetCurrentContext() else {
            throw ColorExtractionError.cgImageCreationFailed
        }
        
        // 为每个颜色创建一个像素，alpha值表示权重
        for (index, color) in colors.enumerated() {
            var red: CGFloat = 0, green: CGFloat = 0, blue: CGFloat = 0, alpha: CGFloat = 0
            color.getRed(&red, green: &green, blue: &blue, alpha: &alpha)
            
            // 计算权重（基于在数组中的位置）
            let weight = 1.0 - (CGFloat(index) / CGFloat(colors.count))
            let weightedColor = UIColor(red: red, green: green, blue: blue, alpha: weight)
            
            weightedColor.setFill()
            context.fill(CGRect(x: index, y: 0, width: 1, height: 1))
        }
        
        guard let resultImage = UIGraphicsGetImageFromCurrentImageContext() else {
            throw ColorExtractionError.cgImageCreationFailed
        }
        
        return resultImage
    }
    
    /// 解析颜色结果
    /// - Parameter outputCIImage: K-Means 输出的 CIImage
    /// - Returns: 提取的颜色数组
    /// - Throws: ColorExtractionError
    private func parseColorResults(from outputCIImage: CIImage) throws -> [ExtractedColor] {
        let context = CIContext(options: nil)
        
        guard let cgImage = context.createCGImage(outputCIImage, from: outputCIImage.extent) else {
            AppLogger.error("CGImage 创建失败", category: .data)
            throw ColorExtractionError.cgImageCreationFailed
        }
        
        let width = cgImage.width
        let _ = cgImage.height
        
        guard let data = cgImage.dataProvider?.data else {
            AppLogger.error("图片数据提取失败", category: .data)
            throw ColorExtractionError.dataExtractionFailed
        }
        
        let pointer: UnsafePointer<UInt8> = CFDataGetBytePtr(data)
        var extractedColors: [ExtractedColor] = []
        
        // 遍历每个像素（即每个聚类颜色）
        for i in 0..<width {
            let offset = i * 4 // 每个像素有 4 个字节 (RGBA)
            let red = CGFloat(pointer[offset]) / 255.0
            let green = CGFloat(pointer[offset + 1]) / 255.0
            let blue = CGFloat(pointer[offset + 2]) / 255.0
            let alpha = CGFloat(pointer[offset + 3]) / 255.0 // Alpha 值表示该颜色的权重
            
            // 过滤掉权重过低的颜色
            if alpha > 0.01 {
                let uiColor = UIColor(red: red, green: green, blue: blue, alpha: alpha)
                let extractedColor = ExtractedColor(uiColor: uiColor)
                extractedColors.append(extractedColor)
            }
        }
        
        // 按照颜色权重降序排序
        let sortedColors = extractedColors.sorted { $0.weight > $1.weight }
        
        guard !sortedColors.isEmpty else {
            AppLogger.warning("未提取到任何颜色", category: .data)
            throw ColorExtractionError.noColorsExtracted
        }
        
        return sortedColors
    }
    
    /// 调整图片尺寸
    /// - Parameter size: 目标尺寸
    /// - Returns: 调整后的图片
    private func resized(to size: CGSize) -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(size, false, scale)
        defer { UIGraphicsEndImageContext() }
        
        draw(in: CGRect(origin: .zero, size: size))
        return UIGraphicsGetImageFromCurrentImageContext()
    }
}

// MARK: - 预览和测试支持

#if DEBUG
extension UIImage {
    /// 创建测试用的彩色图片
    static func createTestColorImage(size: CGSize = CGSize(width: 100, height: 100)) -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(size, false, 0)
        defer { UIGraphicsEndImageContext() }
        
        let context = UIGraphicsGetCurrentContext()
        
        // 创建渐变色彩图片用于测试
        let colors = [UIColor.red, UIColor.green, UIColor.blue, UIColor.yellow]
        let sectionWidth = size.width / CGFloat(colors.count)
        
        for (index, color) in colors.enumerated() {
            color.setFill()
            let rect = CGRect(
                x: CGFloat(index) * sectionWidth,
                y: 0,
                width: sectionWidth,
                height: size.height
            )
            context?.fill(rect)
        }
        
        return UIGraphicsGetImageFromCurrentImageContext()
    }
}
#endif