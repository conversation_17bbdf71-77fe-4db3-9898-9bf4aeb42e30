//
//  ContentView.swift
//  Color Palette X
//
//  Created by <PERSON><PERSON><PERSON> on 2025/7/12.
//

import SwiftUI

struct ContentView: View {
    @State private var selectedTab = 0

    init() {
        AppLogger.info("应用主界面初始化", category: .ui)
    }

    var body: some View {
        //printf NSlog OSLog
        TabView(selection: $selectedTab) {
            WelcomeView()
                .tabItem {
                    Image(systemName: "house.fill")
                    Text(LocalizedStringKey("welcome"))
                }
                .tag(0)

            HomeView()
                .tabItem {
                    Image(systemName: "paintpalette.fill")
                    Text(LocalizedStringKey("color_tools"))
                }
                .tag(1)

            FavoritesView()
                .tabItem {
                    Image(systemName: "bookmark.fill")
                    Text(LocalizedStringKey("favorites"))
                }
                .tag(2)

            ProfileView()
                .tabItem {
                    Image(systemName: "person.fill")
                    Text(LocalizedStringKey("profile"))
                }
                .tag(3)
        }
        .accentColor(.indigo)
        .onChange(of: selectedTab) { _, newValue in
            let tabNames = ["欢迎", "色彩工具", "收藏", "个人资料"]
            if newValue < tabNames.count {
                AppLogger.info("切换到标签页: \(tabNames[newValue])", category: .ui)
            }
        }
    }
}

#Preview {
    ContentView()
}
