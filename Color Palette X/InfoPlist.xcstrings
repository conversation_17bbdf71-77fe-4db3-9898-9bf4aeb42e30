{"sourceLanguage": "en", "strings": {"CFBundleName": {"comment": "Bundle name", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Color Palette X"}}}}, "NSCameraUsageDescription": {"comment": "Camera permission description for color extraction feature", "extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "This app needs camera access to take photos for color extraction. Your photos are processed locally and never shared."}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "此应用需要访问相机以拍摄照片进行颜色提取。您的照片在本地处理，绝不会被分享。"}}}}}, "version": "1.1"}