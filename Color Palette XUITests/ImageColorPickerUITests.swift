//
//  ImageColorPickerUITests.swift
//  Color Palette XUITests
//
//  Created by <PERSON><PERSON> on 2025/7/27.
//

import XCTest

/// 图片取色功能UI测试
final class ImageColorPickerUITests: XCTestCase {
    
    var app: XCUIApplication!
    
    override func setUpWithError() throws {
        continueAfterFailure = false
        app = XCUIApplication()
        app.launch()
    }
    
    override func tearDownWithError() throws {
        app = nil
    }
    
    // MARK: - 导航测试
    
    /// 测试从主页导航到图片取色功能
    func testNavigationToImageColorPicker() throws {
        // 点击色彩工具标签页
        let colorToolsTab = app.tabBars.buttons["色彩工具"]
        XCTAssertTrue(colorToolsTab.exists, "色彩工具标签页应该存在")
        colorToolsTab.tap()
        
        // 查找图片取色工具卡片
        let imageColorExtractorCard = app.buttons["图片取色"]
        XCTAssertTrue(imageColorExtractorCard.waitForExistence(timeout: 5), "图片取色卡片应该存在")
        
        // 点击图片取色卡片
        imageColorExtractorCard.tap()
        
        // 验证导航到图片取色界面
        let imageColorPickerTitle = app.navigationBars["图片取色"]
        XCTAssertTrue(imageColorPickerTitle.waitForExistence(timeout: 5), "应该导航到图片取色界面")
    }
    
    /// 测试图片来源选择弹窗
    func testImageSourceBottomSheet() throws {
        // 导航到图片取色界面
        navigateToImageColorPicker()
        
        // 点击选择图片按钮
        let selectImageButton = app.buttons["选择图片"]
        XCTAssertTrue(selectImageButton.exists, "选择图片按钮应该存在")
        selectImageButton.tap()
        
        // 验证底部弹窗出现
        let bottomSheet = app.sheets.firstMatch
        XCTAssertTrue(bottomSheet.waitForExistence(timeout: 3), "底部弹窗应该出现")
        
        // 验证弹窗选项
        let cameraOption = app.buttons["相机"]
        let photoLibraryOption = app.buttons["相册"]
        let cancelOption = app.buttons["取消"]
        
        XCTAssertTrue(cameraOption.exists, "相机选项应该存在")
        XCTAssertTrue(photoLibraryOption.exists, "相册选项应该存在")
        XCTAssertTrue(cancelOption.exists, "取消选项应该存在")
        
        // 测试取消操作
        cancelOption.tap()
        XCTAssertFalse(bottomSheet.exists, "点击取消后弹窗应该消失")
    }
    
    /// 测试相册选择流程
    func testPhotoLibrarySelection() throws {
        // 导航到图片取色界面
        navigateToImageColorPicker()
        
        // 打开图片来源选择
        app.buttons["选择图片"].tap()
        
        // 选择相册
        let photoLibraryOption = app.buttons["相册"]
        XCTAssertTrue(photoLibraryOption.waitForExistence(timeout: 3), "相册选项应该存在")
        photoLibraryOption.tap()
        
        // 验证相册选择器出现
        // 注意：相册选择器是系统界面，可能需要特殊处理
        let photoPicker = app.otherElements["PhotosPicker"]
        if photoPicker.waitForExistence(timeout: 5) {
            // 如果相册选择器出现，测试取消操作
            let cancelButton = app.buttons["Cancel"]
            if cancelButton.exists {
                cancelButton.tap()
            }
        }
    }
    
    // MARK: - 可访问性测试
    
    /// 测试可访问性标签
    func testAccessibilityLabels() throws {
        navigateToImageColorPicker()
        
        // 测试主要按钮的可访问性
        let selectImageButton = app.buttons["选择图片"]
        XCTAssertTrue(selectImageButton.exists, "选择图片按钮应该存在")
        XCTAssertFalse(selectImageButton.label.isEmpty, "按钮应该有可访问性标签")
        
        // 打开底部弹窗测试选项的可访问性
        selectImageButton.tap()
        
        let cameraOption = app.buttons["相机"]
        if cameraOption.waitForExistence(timeout: 3) {
            XCTAssertFalse(cameraOption.label.isEmpty, "相机选项应该有可访问性标签")
        }
        
        // 关闭弹窗
        app.buttons["取消"].tap()
    }
    
    /// 测试动态字体支持
    func testDynamicTypeSupport() throws {
        // 这个测试需要在不同的字体大小设置下运行
        // 可以通过 XCUIDevice 来改变系统设置，但这比较复杂
        // 这里只做基本的界面存在性检查
        
        navigateToImageColorPicker()
        
        let selectImageButton = app.buttons["选择图片"]
        XCTAssertTrue(selectImageButton.exists, "在默认字体大小下按钮应该存在")
        
        // 在实际测试中，可以通过改变系统字体大小来测试适应性
        // 这需要更复杂的测试设置
    }
    
    // MARK: - 错误处理测试
    
    /// 测试权限拒绝处理
    func testPermissionDeniedHandling() throws {
        // 这个测试需要模拟权限被拒绝的情况
        // 在实际测试中，可能需要使用模拟器的权限设置
        
        navigateToImageColorPicker()
        app.buttons["选择图片"].tap()
        
        // 选择相机（如果在模拟器中，相机通常不可用）
        let cameraOption = app.buttons["相机"]
        if cameraOption.waitForExistence(timeout: 3) {
            cameraOption.tap()
            
            // 检查是否出现权限相关的提示
            // 这取决于具体的权限状态和设备类型
        }
    }
    
    // MARK: - 性能测试
    
    /// 测试界面响应性能
    func testUIResponsiveness() throws {
        measure(metrics: [XCTApplicationLaunchMetric()]) {
            app.launch()
        }
        
        // 测试导航性能
        measure(metrics: [XCTOSSignpostMetric.navigationTransition]) {
            navigateToImageColorPicker()
        }
    }
    
    // MARK: - 辅助方法
    
    /// 导航到图片取色界面
    private func navigateToImageColorPicker() {
        // 点击色彩工具标签页
        let colorToolsTab = app.tabBars.buttons["色彩工具"]
        if colorToolsTab.exists {
            colorToolsTab.tap()
        }
        
        // 点击图片取色卡片
        let imageColorExtractorCard = app.buttons["图片取色"]
        if imageColorExtractorCard.waitForExistence(timeout: 5) {
            imageColorExtractorCard.tap()
        }
        
        // 等待界面加载
        let imageColorPickerTitle = app.navigationBars["图片取色"]
        XCTAssertTrue(imageColorPickerTitle.waitForExistence(timeout: 5), "应该成功导航到图片取色界面")
    }
    
    /// 等待元素出现
    private func waitForElement(_ element: XCUIElement, timeout: TimeInterval = 5) -> Bool {
        return element.waitForExistence(timeout: timeout)
    }
    
    /// 检查元素是否可见且可交互
    private func isElementInteractable(_ element: XCUIElement) -> Bool {
        return element.exists && element.isHittable
    }
}

// MARK: - 测试扩展

extension ImageColorPickerUITests {
    
    /// 测试横屏模式下的界面
    func testLandscapeOrientation() throws {
        // 旋转到横屏
        XCUIDevice.shared.orientation = .landscapeLeft
        
        navigateToImageColorPicker()
        
        // 验证界面在横屏下仍然正常工作
        let selectImageButton = app.buttons["选择图片"]
        XCTAssertTrue(selectImageButton.exists, "横屏模式下按钮应该存在")
        
        // 测试底部弹窗在横屏下的表现
        selectImageButton.tap()
        let bottomSheet = app.sheets.firstMatch
        XCTAssertTrue(bottomSheet.waitForExistence(timeout: 3), "横屏模式下底部弹窗应该正常显示")
        
        // 关闭弹窗
        app.buttons["取消"].tap()
        
        // 恢复竖屏
        XCUIDevice.shared.orientation = .portrait
    }
    
    /// 测试深色模式下的界面
    func testDarkModeInterface() throws {
        // 注意：改变外观模式需要特殊的测试设置
        // 这里只做基本的界面检查
        
        navigateToImageColorPicker()
        
        let selectImageButton = app.buttons["选择图片"]
        XCTAssertTrue(selectImageButton.exists, "深色模式下按钮应该存在")
        
        // 测试界面元素在深色模式下的可见性
        selectImageButton.tap()
        let cameraOption = app.buttons["相机"]
        let photoLibraryOption = app.buttons["相册"]
        
        XCTAssertTrue(cameraOption.waitForExistence(timeout: 3), "深色模式下相机选项应该可见")
        XCTAssertTrue(photoLibraryOption.exists, "深色模式下相册选项应该可见")
        
        app.buttons["取消"].tap()
    }
    
    /// 测试界面元素的交互反馈
    func testInteractionFeedback() throws {
        navigateToImageColorPicker()
        
        let selectImageButton = app.buttons["选择图片"]
        XCTAssertTrue(selectImageButton.exists, "选择图片按钮应该存在")
        
        // 测试按钮点击反馈
        selectImageButton.tap()
        
        // 验证弹窗动画和交互
        let bottomSheet = app.sheets.firstMatch
        XCTAssertTrue(bottomSheet.waitForExistence(timeout: 3), "底部弹窗应该出现")
        
        // 测试选项按钮的交互
        let cameraOption = app.buttons["相机"]
        let photoLibraryOption = app.buttons["相册"]
        let cancelOption = app.buttons["取消"]
        
        // 验证所有按钮都可以交互
        XCTAssertTrue(isElementInteractable(cameraOption), "相机选项应该可以交互")
        XCTAssertTrue(isElementInteractable(photoLibraryOption), "相册选项应该可以交互")
        XCTAssertTrue(isElementInteractable(cancelOption), "取消选项应该可以交互")
        
        // 测试取消交互
        cancelOption.tap()
        XCTAssertFalse(bottomSheet.exists, "点击取消后弹窗应该消失")
    }
    
    /// 测试导航栏功能
    func testNavigationBarFunctionality() throws {
        navigateToImageColorPicker()
        
        // 验证导航栏标题
        let navigationBar = app.navigationBars["图片取色"]
        XCTAssertTrue(navigationBar.exists, "导航栏应该存在")
        
        // 测试返回按钮
        let backButton = navigationBar.buttons.firstMatch
        if backButton.exists {
            backButton.tap()
            
            // 验证返回到主界面
            let colorToolsTab = app.tabBars.buttons["色彩工具"]
            XCTAssertTrue(colorToolsTab.waitForExistence(timeout: 3), "应该返回到主界面")
        }
    }
    
    /// 测试错误状态显示
    func testErrorStateDisplay() throws {
        navigateToImageColorPicker()
        
        // 这个测试需要模拟错误状态
        // 在实际应用中，可能需要通过特殊的测试配置来触发错误
        
        let selectImageButton = app.buttons["选择图片"]
        selectImageButton.tap()
        
        // 选择相机（在模拟器中通常会失败）
        let cameraOption = app.buttons["相机"]
        if cameraOption.waitForExistence(timeout: 3) {
            cameraOption.tap()
            
            // 检查是否出现错误提示
            // 这取决于具体的错误处理实现
            let errorAlert = app.alerts.firstMatch
            if errorAlert.waitForExistence(timeout: 5) {
                // 如果出现错误提示，测试确认按钮
                let okButton = errorAlert.buttons["确定"]
                if okButton.exists {
                    okButton.tap()
                }
            }
        }
    }
    
    /// 测试多次操作的稳定性
    func testMultipleOperationsStability() throws {
        navigateToImageColorPicker()
        
        // 重复执行多次操作
        for i in 0..<3 {
            let selectImageButton = app.buttons["选择图片"]
            XCTAssertTrue(selectImageButton.exists, "第 \(i+1) 次操作：选择图片按钮应该存在")
            
            selectImageButton.tap()
            
            let bottomSheet = app.sheets.firstMatch
            XCTAssertTrue(bottomSheet.waitForExistence(timeout: 3), "第 \(i+1) 次操作：底部弹窗应该出现")
            
            // 取消操作
            let cancelOption = app.buttons["取消"]
            XCTAssertTrue(cancelOption.exists, "第 \(i+1) 次操作：取消按钮应该存在")
            cancelOption.tap()
            
            XCTAssertFalse(bottomSheet.exists, "第 \(i+1) 次操作：点击取消后弹窗应该消失")
            
            // 短暂等待，确保界面稳定
            Thread.sleep(forTimeInterval: 0.5)
        }
    }
    
    /// 测试界面布局适应性
    func testLayoutAdaptability() throws {
        navigateToImageColorPicker()
        
        // 测试不同屏幕方向下的布局
        let orientations: [UIDeviceOrientation] = [.portrait, .landscapeLeft, .landscapeRight]
        
        for orientation in orientations {
            XCUIDevice.shared.orientation = orientation
            
            // 等待界面调整
            Thread.sleep(forTimeInterval: 1.0)
            
            // 验证关键元素仍然可见和可交互
            let selectImageButton = app.buttons["选择图片"]
            XCTAssertTrue(selectImageButton.exists, "在 \(orientation) 方向下按钮应该存在")
            XCTAssertTrue(isElementInteractable(selectImageButton), "在 \(orientation) 方向下按钮应该可交互")
        }
        
        // 恢复到竖屏
        XCUIDevice.shared.orientation = .portrait
    }
}