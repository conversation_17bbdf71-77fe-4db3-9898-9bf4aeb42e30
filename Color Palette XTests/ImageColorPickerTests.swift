//
//  ImageColorPickerTests.swift
//  Color Palette XTests
//
//  Created by <PERSON><PERSON> on 2025/7/27.
//

import XCTest
@testable import Color_Palette_X

/// 图片取色功能测试
final class ImageColorPickerTests: XCTestCase {
    
    // MARK: - 测试属性
    
    var colorExtractionService: ColorExtractionService!
    var cameraService: CameraService!
    var photoLibraryService: PhotoLibraryService!
    
    // MARK: - 测试生命周期
    
    override func setUpWithError() throws {
        colorExtractionService = ColorExtractionService()
        cameraService = CameraService()
        photoLibraryService = PhotoLibraryService()
    }
    
    override func tearDownWithError() throws {
        colorExtractionService = nil
        cameraService = nil
        photoLibraryService = nil
    }
    
    // MARK: - 颜色提取测试
    
    /// 测试颜色提取服务基本功能
    func testColorExtractionService() async throws {
        // 创建测试图片
        guard let testImage = UIImage.createTestColorImage() else {
            XCTFail("无法创建测试图片")
            return
        }
        
        // 测试颜色提取
        let colors = try await colorExtractionService.extractDominantColors(from: testImage)
        
        // 验证结果
        XCTAssertFalse(colors.isEmpty, "应该提取到颜色")
        XCTAssertLessThanOrEqual(colors.count, 8, "提取的颜色数量不应超过默认值")
        
        // 验证颜色按权重排序
        for i in 0..<colors.count-1 {
            XCTAssertGreaterThanOrEqual(colors[i].weight, colors[i+1].weight, "颜色应该按权重降序排列")
        }
    }
    
    /// 测试颜色提取参数
    func testColorExtractionParameters() async throws {
        guard let testImage = UIImage.createTestColorImage() else {
            XCTFail("无法创建测试图片")
            return
        }
        
        // 测试不同的颜色数量
        let colors5 = try await colorExtractionService.extractDominantColors(from: testImage, count: 5)
        XCTAssertLessThanOrEqual(colors5.count, 5, "应该提取最多5个颜色")
        
        let colors10 = try await colorExtractionService.extractDominantColors(from: testImage, count: 10)
        XCTAssertLessThanOrEqual(colors10.count, 10, "应该提取最多10个颜色")
    }
    
    /// 测试颜色提取缓存
    func testColorExtractionCache() async throws {
        guard let testImage = UIImage.createTestColorImage() else {
            XCTFail("无法创建测试图片")
            return
        }
        
        // 第一次提取
        let startTime1 = Date()
        let colors1 = try await colorExtractionService.extractDominantColors(from: testImage)
        let duration1 = Date().timeIntervalSince(startTime1)
        
        // 第二次提取（应该使用缓存）
        let startTime2 = Date()
        let colors2 = try await colorExtractionService.extractDominantColors(from: testImage)
        let duration2 = Date().timeIntervalSince(startTime2)
        
        // 验证结果一致
        XCTAssertEqual(colors1.count, colors2.count, "缓存的结果应该一致")
        
        // 验证缓存提升了性能（第二次应该更快）
        XCTAssertLessThan(duration2, duration1, "缓存应该提升性能")
    }
    
    // MARK: - 数据模型测试
    
    /// 测试 ExtractedColor 模型
    func testExtractedColorModel() {
        let testColor = UIColor.red
        let extractedColor = ExtractedColor(uiColor: testColor)
        
        XCTAssertNotNil(extractedColor.hexString, "应该有十六进制字符串")
        XCTAssertTrue(extractedColor.hexString.hasPrefix("#"), "十六进制字符串应该以#开头")
        XCTAssertEqual(extractedColor.hexString.count, 7, "十六进制字符串应该是7个字符")
        XCTAssertGreaterThanOrEqual(extractedColor.weight, 0, "权重应该大于等于0")
        XCTAssertLessThanOrEqual(extractedColor.weight, 1, "权重应该小于等于1")
        
        // 测试颜色相等性
        let sameColor = ExtractedColor(uiColor: testColor)
        XCTAssertEqual(extractedColor.hexString, sameColor.hexString, "相同颜色应该有相同的十六进制值")
        
        // 测试不同颜色
        let differentColor = ExtractedColor(uiColor: .blue)
        XCTAssertNotEqual(extractedColor.hexString, differentColor.hexString, "不同颜色应该有不同的十六进制值")
    }
    
    /// 测试 ImageSource 枚举
    func testImageSourceEnum() {
        let allSources = ImageSource.allCases
        XCTAssertEqual(allSources.count, 3, "应该有3个图片来源选项")
        
        for source in allSources {
            XCTAssertFalse(source.icon.isEmpty, "每个来源都应该有图标")
            // 注意：这里不能直接测试 LocalizedStringKey，需要在UI测试中验证
        }
    }
    
    /// 测试 ColorExtractionState 枚举
    func testColorExtractionState() {
        let idleState = ColorExtractionState.idle
        XCTAssertFalse(idleState.isLoading, "idle状态不应该是加载中")
        XCTAssertFalse(idleState.hasError, "idle状态不应该有错误")
        XCTAssertFalse(idleState.hasResults, "idle状态不应该有结果")
        
        let loadingState = ColorExtractionState.loading
        XCTAssertTrue(loadingState.isLoading, "loading状态应该是加载中")
        
        let errorState = ColorExtractionState.error("测试错误")
        XCTAssertTrue(errorState.hasError, "error状态应该有错误")
        XCTAssertEqual(errorState.errorMessage, "测试错误", "错误消息应该匹配")
        
        let successState = ColorExtractionState.success([])
        XCTAssertFalse(successState.hasResults, "空结果不应该有结果")
        
        let successWithResults = ColorExtractionState.success(ExtractedColor.samples)
        XCTAssertTrue(successWithResults.hasResults, "有结果的成功状态应该有结果")
    }
    
    // MARK: - 服务测试
    
    /// 测试相机服务
    func testCameraService() {
        // 测试相机可用性检查
        let isAvailable = cameraService.isCameraAvailable()
        // 注意：在模拟器中相机通常不可用，在真机上才可用
        // XCTAssertTrue(isAvailable, "相机应该可用") // 这个测试在模拟器中会失败
        
        // 测试创建相机控制器
        if isAvailable {
            XCTAssertNoThrow(try cameraService.createCameraController(), "创建相机控制器不应该抛出异常")
        } else {
            XCTAssertThrowsError(try cameraService.createCameraController(), "相机不可用时应该抛出异常")
        }
    }
    
    /// 测试相册服务
    func testPhotoLibraryService() {
        // 测试相册可用性检查
        let isAvailable = photoLibraryService.isPhotoLibraryAvailable()
        XCTAssertTrue(isAvailable, "相册选择器应该可用")
        
        // 测试创建相册选择器
        XCTAssertNoThrow(photoLibraryService.createPhotoPickerController(), "创建相册选择器不应该抛出异常")
    }
    
    // MARK: - 性能测试
    
    /// 测试颜色提取性能
    func testColorExtractionPerformance() throws {
        guard let testImage = UIImage.createTestColorImage(size: CGSize(width: 500, height: 500)) else {
            XCTFail("无法创建测试图片")
            return
        }
        
        measure {
            let expectation = XCTestExpectation(description: "颜色提取完成")
            
            Task {
                do {
                    _ = try await colorExtractionService.extractDominantColors(from: testImage)
                    expectation.fulfill()
                } catch {
                    XCTFail("颜色提取失败: \(error)")
                    expectation.fulfill()
                }
            }
            
            wait(for: [expectation], timeout: 10.0)
        }
    }
    
    // MARK: - 错误处理测试
    
    /// 测试无效图片处理
    func testInvalidImageHandling() async {
        // 创建一个空的图片
        let emptyImage = UIImage()
        
        do {
            _ = try await colorExtractionService.extractDominantColors(from: emptyImage)
            XCTFail("空图片应该抛出错误")
        } catch {
            // 预期的错误
            XCTAssertTrue(true, "空图片正确抛出了错误")
        }
    }
    
    /// 测试取消操作
    func testCancellation() async {
        guard let testImage = UIImage.createTestColorImage() else {
            XCTFail("无法创建测试图片")
            return
        }
        
        // 开始提取
        let extractionTask = Task {
            try await colorExtractionService.extractDominantColors(from: testImage)
        }
        
        // 立即取消
        colorExtractionService.cancelCurrentTask()
        
        do {
            _ = try await extractionTask.value
            // 如果没有被取消，也是可以的（取决于时机）
        } catch {
            // 如果被取消了，也是预期的
            XCTAssertTrue(true, "取消操作正常工作")
        }
    }
}

// MARK: - 视图模型测试

/// 测试 ImageColorPickerViewModel
func testImageColorPickerViewModel() {
    let viewModel = ImageColorPickerViewModel()
    
    // 测试初始状态
    XCTAssertFalse(viewModel.showingImageSourceSheet, "初始状态不应该显示底部弹窗")
    XCTAssertNil(viewModel.selectedImage, "初始状态不应该有选中的图片")
    XCTAssertEqual(viewModel.navigationPath.count, 0, "初始状态导航路径应该为空")
    
    // 测试显示底部弹窗
    viewModel.showImageSourceSheet()
    XCTAssertTrue(viewModel.showingImageSourceSheet, "调用后应该显示底部弹窗")
    
    // 测试隐藏底部弹窗
    viewModel.hideImageSourceSheet()
    XCTAssertFalse(viewModel.showingImageSourceSheet, "调用后应该隐藏底部弹窗")
    
    // 测试图片选择
    let testImage = UIImage.createTestColorImage()
    viewModel.handleImageSelection(testImage)
    XCTAssertEqual(viewModel.selectedImage, testImage, "应该设置选中的图片")
    XCTAssertEqual(viewModel.navigationPath.count, 1, "应该导航到颜色提取界面")
}

/// 测试 ColorExtractionViewModel
func testColorExtractionViewModel() async {
    guard let testImage = UIImage.createTestColorImage() else {
        XCTFail("无法创建测试图片")
        return
    }
    
    let viewModel = ColorExtractionViewModel()
    
    // 测试初始状态
    XCTAssertEqual(viewModel.extractionState, .idle, "初始状态应该是idle")
    
    // 测试开始颜色提取
    await viewModel.extractColors(from: testImage)
    
    // 验证最终状态
    switch viewModel.extractionState {
    case .success(let colors):
        XCTAssertFalse(colors.isEmpty, "应该提取到颜色")
    case .error(let message):
        XCTFail("颜色提取失败: \(message)")
    default:
        XCTFail("颜色提取后状态不正确")
    }
}

// MARK: - 集成测试

/// 测试完整的用户流程
func testCompleteUserFlow() async {
    guard let testImage = UIImage.createTestColorImage() else {
        XCTFail("无法创建测试图片")
        return
    }
    
    // 1. 创建主视图模型
    let mainViewModel = ImageColorPickerViewModel()
    
    // 2. 显示图片来源选择
    mainViewModel.showImageSourceSheet()
    XCTAssertTrue(mainViewModel.showingImageSourceSheet, "应该显示图片来源选择")
    
    // 3. 选择图片
    mainViewModel.handleImageSelection(testImage)
    XCTAssertEqual(mainViewModel.selectedImage, testImage, "应该设置选中的图片")
    XCTAssertFalse(mainViewModel.showingImageSourceSheet, "选择图片后应该隐藏弹窗")
    
    // 4. 创建颜色提取视图模型
    let colorViewModel = ColorExtractionViewModel()
    
    // 5. 执行颜色提取
    await colorViewModel.extractColors(from: testImage)
    
    // 6. 验证结果
    XCTAssertTrue(colorViewModel.extractionState.hasResults, "应该有颜色提取结果")
}

// MARK: - 内存和性能测试

/// 测试内存使用
func testMemoryUsage() {
    let initialMemory = getMemoryUsage()
    
    // 创建多个视图模型实例
    var viewModels: [ImageColorPickerViewModel] = []
    for _ in 0..<10 {
        viewModels.append(ImageColorPickerViewModel())
    }
    
    let afterCreationMemory = getMemoryUsage()
    
    // 清理
    viewModels.removeAll()
    
    // 强制垃圾回收
    autoreleasepool {
        // 空的自动释放池，帮助清理内存
    }
    
    let afterCleanupMemory = getMemoryUsage()
    
    // 验证内存没有显著泄漏
    let memoryIncrease = afterCleanupMemory - initialMemory
    XCTAssertLessThan(memoryIncrease, 10 * 1024 * 1024, "内存增长不应超过10MB") // 10MB阈值
}

/// 获取当前内存使用量
private func getMemoryUsage() -> UInt64 {
    var info = mach_task_basic_info()
    var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
    
    let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
        $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
            task_info(mach_task_self_,
                     task_flavor_t(MACH_TASK_BASIC_INFO),
                     $0,
                     &count)
        }
    }
    
    if kerr == KERN_SUCCESS {
        return info.resident_size
    } else {
        return 0
    }
}

// MARK: - 测试扩展

extension ImageColorPickerTests {
    
    /// 创建测试用的视图模型
    func createTestViewModel() -> ImageColorPickerViewModel {
        return ImageColorPickerViewModel()
    }
    
    /// 创建测试用的颜色提取视图模型
    func createTestColorExtractionViewModel() -> ColorExtractionViewModel {
        return ColorExtractionViewModel()
    }
}
// MA
RK: - 边界条件测试

/// 测试极端参数值
func testExtremeParameters() async {
    guard let testImage = UIImage.createTestColorImage() else {
        XCTFail("无法创建测试图片")
        return
    }
    
    // 测试最小颜色数量
    do {
        let colors = try await colorExtractionService.extractDominantColors(from: testImage, count: 1)
        XCTAssertEqual(colors.count, 1, "应该只提取1个颜色")
    } catch {
        XCTFail("提取1个颜色失败: \(error)")
    }
    
    // 测试最大颜色数量
    do {
        let colors = try await colorExtractionService.extractDominantColors(from: testImage, count: 32)
        XCTAssertLessThanOrEqual(colors.count, 32, "提取的颜色数量不应超过32")
    } catch {
        XCTFail("提取32个颜色失败: \(error)")
    }
}

/// 测试不同尺寸的图片
func testDifferentImageSizes() async {
    let sizes = [
        CGSize(width: 10, height: 10),      // 很小的图片
        CGSize(width: 100, height: 100),    // 小图片
        CGSize(width: 500, height: 500),    // 中等图片
        CGSize(width: 1000, height: 1000)   // 大图片
    ]
    
    for size in sizes {
        guard let testImage = UIImage.createTestColorImage(size: size) else {
            XCTFail("无法创建尺寸为 \(size) 的测试图片")
            continue
        }
        
        do {
            let colors = try await colorExtractionService.extractDominantColors(from: testImage)
            XCTAssertFalse(colors.isEmpty, "尺寸为 \(size) 的图片应该能提取到颜色")
        } catch {
            XCTFail("尺寸为 \(size) 的图片颜色提取失败: \(error)")
        }
    }
}

/// 测试并发颜色提取
func testConcurrentColorExtraction() async {
    guard let testImage = UIImage.createTestColorImage() else {
        XCTFail("无法创建测试图片")
        return
    }
    
    // 创建多个并发任务
    let taskCount = 5
    let tasks = (0..<taskCount).map { index in
        Task {
            do {
                let colors = try await colorExtractionService.extractDominantColors(from: testImage)
                return (index, colors)
            } catch {
                XCTFail("并发任务 \(index) 失败: \(error)")
                return (index, [ExtractedColor]())
            }
        }
    }
    
    // 等待所有任务完成
    var results: [(Int, [ExtractedColor])] = []
    for task in tasks {
        let result = await task.value
        results.append(result)
    }
    
    // 验证所有任务都成功完成
    XCTAssertEqual(results.count, taskCount, "所有并发任务都应该完成")
    
    // 验证结果一致性（相同图片应该产生相同结果）
    let firstResult = results[0].1
    for (index, colors) in results {
        XCTAssertEqual(colors.count, firstResult.count, "任务 \(index) 的结果数量应该一致")
    }
}

// MARK: - 国际化测试

/// 测试本地化字符串
func testLocalization() {
    // 测试 ImageSource 的本地化
    for source in ImageSource.allCases {
        let localizedTitle = source.title
        XCTAssertNotNil(localizedTitle, "ImageSource 应该有本地化标题")
    }
    
    // 测试错误消息的本地化
    let errors: [UIImage.ColorExtractionError] = [
        .invalidImage,
        .ciImageCreationFailed,
        .filterFailed,
        .cgImageCreationFailed,
        .dataExtractionFailed,
        .noColorsExtracted
    ]
    
    for error in errors {
        XCTAssertNotNil(error.errorDescription, "错误应该有本地化描述")
        XCTAssertFalse(error.errorDescription?.isEmpty ?? true, "错误描述不应该为空")
    }
}

// MARK: - 可访问性测试

/// 测试可访问性支持
func testAccessibilitySupport() {
    // 测试 ExtractedColor 的可访问性描述
    let testColor = UIColor.red
    let extractedColor = ExtractedColor(uiColor: testColor)
    
    XCTAssertNotNil(extractedColor.accessibilityDescription, "ExtractedColor 应该有可访问性描述")
    XCTAssertFalse(extractedColor.accessibilityDescription.isEmpty, "可访问性描述不应该为空")
}

// MARK: - 数据持久化测试

/// 测试颜色数据的序列化和反序列化
func testColorSerialization() {
    let originalColor = ExtractedColor(uiColor: .blue)
    
    // 测试 JSON 编码
    do {
        let encoder = JSONEncoder()
        let data = try encoder.encode(originalColor)
        
        // 测试 JSON 解码
        let decoder = JSONDecoder()
        let decodedColor = try decoder.decode(ExtractedColor.self, from: data)
        
        // 验证数据一致性
        XCTAssertEqual(originalColor.hexString, decodedColor.hexString, "十六进制字符串应该一致")
        XCTAssertEqual(originalColor.weight, decodedColor.weight, accuracy: 0.001, "权重应该一致")
        
    } catch {
        XCTFail("颜色序列化失败: \(error)")
    }
}

// MARK: - 错误恢复测试

/// 测试从错误状态恢复
func testErrorRecovery() async {
    let viewModel = ColorExtractionViewModel()
    
    // 先触发一个错误
    let emptyImage = UIImage()
    await viewModel.extractColors(from: emptyImage)
    
    // 验证错误状态
    XCTAssertTrue(viewModel.extractionState.hasError, "应该处于错误状态")
    
    // 尝试恢复
    guard let validImage = UIImage.createTestColorImage() else {
        XCTFail("无法创建有效的测试图片")
        return
    }
    
    await viewModel.extractColors(from: validImage)
    
    // 验证恢复成功
    XCTAssertFalse(viewModel.extractionState.hasError, "应该从错误状态恢复")
    XCTAssertTrue(viewModel.extractionState.hasResults, "应该有有效结果")
}

// MARK: - 资源管理测试

/// 测试资源清理
func testResourceCleanup() {
    var viewModel: ColorExtractionViewModel? = ColorExtractionViewModel()
    
    // 创建弱引用来检测内存释放
    weak var weakViewModel = viewModel
    
    // 释放强引用
    viewModel = nil
    
    // 验证对象被正确释放
    XCTAssertNil(weakViewModel, "ViewModel 应该被正确释放")
}

// MARK: - 线程安全测试

/// 测试线程安全性
func testThreadSafety() async {
    guard let testImage = UIImage.createTestColorImage() else {
        XCTFail("无法创建测试图片")
        return
    }
    
    let viewModel = ColorExtractionViewModel()
    
    // 在多个线程上同时调用方法
    await withTaskGroup(of: Void.self) { group in
        for i in 0..<10 {
            group.addTask {
                await viewModel.extractColors(from: testImage)
                AppLogger.debug("线程 \(i) 完成颜色提取", category: .performance)
            }
        }
    }
    
    // 验证最终状态是有效的
    XCTAssertTrue(viewModel.extractionState.hasResults || viewModel.extractionState.hasError, 
                  "最终状态应该是有效的")
}
/
/ MARK: - 测试辅助扩展

extension ExtractedColor {
    /// 创建测试用的颜色样本
    static var samples: [ExtractedColor] {
        return [
            ExtractedColor(uiColor: .red),
            ExtractedColor(uiColor: .green),
            ExtractedColor(uiColor: .blue),
            ExtractedColor(uiColor: .yellow),
            ExtractedColor(uiColor: .purple)
        ]
    }
    
    /// 可访问性描述（用于测试）
    var accessibilityDescription: String {
        return "颜色 \(hexString)，权重 \(String(format: "%.1f%%", weight * 100))"
    }
}

extension ExtractedColor: Codable {
    enum CodingKeys: String, CodingKey {
        case red, green, blue, alpha, weight, hexString
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        let red = try container.decode(CGFloat.self, forKey: .red)
        let green = try container.decode(CGFloat.self, forKey: .green)
        let blue = try container.decode(CGFloat.self, forKey: .blue)
        let alpha = try container.decode(CGFloat.self, forKey: .alpha)
        
        self.init(uiColor: UIColor(red: red, green: green, blue: blue, alpha: alpha))
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        
        var red: CGFloat = 0
        var green: CGFloat = 0
        var blue: CGFloat = 0
        var alpha: CGFloat = 0
        
        uiColor.getRed(&red, green: &green, blue: &blue, alpha: &alpha)
        
        try container.encode(red, forKey: .red)
        try container.encode(green, forKey: .green)
        try container.encode(blue, forKey: .blue)
        try container.encode(alpha, forKey: .alpha)
        try container.encode(weight, forKey: .weight)
        try container.encode(hexString, forKey: .hexString)
    }
}

extension ImageSource {
    /// 本地化标题（用于测试）
    var title: LocalizedStringKey {
        switch self {
        case .camera:
            return "camera"
        case .photoLibrary:
            return "photo_library"
        case .cancel:
            return "cancel"
        }
    }
}

extension ColorExtractionState: Equatable {
    static func == (lhs: ColorExtractionState, rhs: ColorExtractionState) -> Bool {
        switch (lhs, rhs) {
        case (.idle, .idle):
            return true
        case (.loading, .loading):
            return true
        case (.success(let lhsColors), .success(let rhsColors)):
            return lhsColors.count == rhsColors.count
        case (.error(let lhsMessage), .error(let rhsMessage)):
            return lhsMessage == rhsMessage
        default:
            return false
        }
    }
}

// MARK: - Mock 服务扩展

extension CameraService {
    static func createMockService() -> CameraService {
        return CameraService()
    }
}

extension PhotoLibraryService {
    static func createMockService() -> PhotoLibraryService {
        return PhotoLibraryService()
    }
}

extension ColorExtractionService {
    static func createMockService() -> ColorExtractionService {
        return ColorExtractionService()
    }
}

// MARK: - 测试数据生成器

extension UIImage {
    /// 创建渐变测试图片
    static func createGradientTestImage(size: CGSize = CGSize(width: 200, height: 200)) -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(size, false, 0)
        defer { UIGraphicsEndImageContext() }
        
        guard let context = UIGraphicsGetCurrentContext() else { return nil }
        
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        let colors = [UIColor.red.cgColor, UIColor.blue.cgColor]
        let locations: [CGFloat] = [0.0, 1.0]
        
        guard let gradient = CGGradient(colorsSpace: colorSpace, colors: colors as CFArray, locations: locations) else {
            return nil
        }
        
        let startPoint = CGPoint(x: 0, y: 0)
        let endPoint = CGPoint(x: size.width, y: size.height)
        
        context.drawLinearGradient(gradient, start: startPoint, end: endPoint, options: [])
        
        return UIGraphicsGetImageFromCurrentImageContext()
    }
    
    /// 创建纯色测试图片
    static func createSolidColorTestImage(color: UIColor, size: CGSize = CGSize(width: 100, height: 100)) -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(size, false, 0)
        defer { UIGraphicsEndImageContext() }
        
        color.setFill()
        UIRectFill(CGRect(origin: .zero, size: size))
        
        return UIGraphicsGetImageFromCurrentImageContext()
    }
    
    /// 创建复杂图案测试图片
    static func createComplexPatternTestImage(size: CGSize = CGSize(width: 300, height: 300)) -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(size, false, 0)
        defer { UIGraphicsEndImageContext() }
        
        guard let context = UIGraphicsGetCurrentContext() else { return nil }
        
        // 创建棋盘图案
        let squareSize: CGFloat = 20
        let colors = [UIColor.black, UIColor.white]
        
        for row in 0..<Int(size.height / squareSize) {
            for col in 0..<Int(size.width / squareSize) {
                let colorIndex = (row + col) % 2
                colors[colorIndex].setFill()
                
                let rect = CGRect(
                    x: CGFloat(col) * squareSize,
                    y: CGFloat(row) * squareSize,
                    width: squareSize,
                    height: squareSize
                )
                context.fill(rect)
            }
        }
        
        return UIGraphicsGetImageFromCurrentImageContext()
    }
}