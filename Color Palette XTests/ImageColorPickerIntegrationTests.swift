//
//  ImageColorPickerIntegrationTests.swift
//  Color Palette XTests
//
//  Created by <PERSON><PERSON> on 2025/7/27.
//

import XCTest
@testable import Color_Palette_X

/// 图片取色功能集成测试
/// 
/// 测试各个组件之间的集成和完整的用户流程
final class ImageColorPickerIntegrationTests: XCTestCase {
    
    // MARK: - 测试属性
    
    var mainViewModel: ImageColorPickerViewModel!
    var colorExtractionViewModel: ColorExtractionViewModel!
    
    // MARK: - 测试生命周期
    
    override func setUpWithError() throws {
        mainViewModel = ImageColorPickerViewModel()
        colorExtractionViewModel = ColorExtractionViewModel()
    }
    
    override func tearDownWithError() throws {
        mainViewModel = nil
        colorExtractionViewModel = nil
    }
    
    // MARK: - 完整流程测试
    
    /// 测试完整的图片选择和颜色提取流程
    func testCompleteImageColorExtractionFlow() async throws {
        // 1. 初始状态验证
        XCTAssertFalse(mainViewModel.showingImageSourceSheet, "初始状态不应该显示底部弹窗")
        XCTAssertNil(mainViewModel.selectedImage, "初始状态不应该有选中的图片")
        XCTAssertEqual(colorExtractionViewModel.extractionState, .idle, "颜色提取应该处于idle状态")
        
        // 2. 显示图片来源选择
        mainViewModel.showImageSourceSheet()
        XCTAssertTrue(mainViewModel.showingImageSourceSheet, "应该显示图片来源选择弹窗")
        
        // 3. 模拟图片选择
        guard let testImage = UIImage.createTestColorImage() else {
            XCTFail("无法创建测试图片")
            return
        }
        
        mainViewModel.handleImageSelection(testImage)
        
        // 4. 验证图片选择结果
        XCTAssertEqual(mainViewModel.selectedImage, testImage, "应该设置选中的图片")
        XCTAssertFalse(mainViewModel.showingImageSourceSheet, "选择图片后应该隐藏弹窗")
        XCTAssertEqual(mainViewModel.navigationPath.count, 1, "应该导航到颜色提取界面")
        
        // 5. 执行颜色提取
        await colorExtractionViewModel.extractColors(from: testImage)
        
        // 6. 验证颜色提取结果
        switch colorExtractionViewModel.extractionState {
        case .success(let colors):
            XCTAssertFalse(colors.isEmpty, "应该提取到颜色")
            XCTAssertLessThanOrEqual(colors.count, 8, "提取的颜色数量不应超过默认值")
            
            // 验证颜色按权重排序
            for i in 0..<colors.count-1 {
                XCTAssertGreaterThanOrEqual(colors[i].weight, colors[i+1].weight, 
                                          "颜色应该按权重降序排列")
            }
            
        case .error(let message):
            XCTFail("颜色提取失败: \(message)")
            
        default:
            XCTFail("颜色提取后状态不正确: \(colorExtractionViewModel.extractionState)")
        }
    }
    
    /// 测试重新选择图片的流程
    func testReSelectImageFlow() async throws {
        // 1. 选择第一张图片
        guard let firstImage = UIImage.createTestColorImage(size: CGSize(width: 100, height: 100)) else {
            XCTFail("无法创建第一张测试图片")
            return
        }
        
        mainViewModel.handleImageSelection(firstImage)
        await colorExtractionViewModel.extractColors(from: firstImage)
        
        let firstResults = colorExtractionViewModel.extractionState.colors
        XCTAssertFalse(firstResults.isEmpty, "第一次提取应该有结果")
        
        // 2. 重新选择图片
        colorExtractionViewModel.resetState()
        XCTAssertEqual(colorExtractionViewModel.extractionState, .idle, "重置后应该回到idle状态")
        
        // 3. 选择第二张图片
        guard let secondImage = UIImage.createTestColorImage(size: CGSize(width: 200, height: 200)) else {
            XCTFail("无法创建第二张测试图片")
            return
        }
        
        mainViewModel.handleImageSelection(secondImage)
        await colorExtractionViewModel.extractColors(from: secondImage)
        
        // 4. 验证新的结果
        let secondResults = colorExtractionViewModel.extractionState.colors
        XCTAssertFalse(secondResults.isEmpty, "第二次提取应该有结果")
        XCTAssertEqual(mainViewModel.selectedImage, secondImage, "应该更新为新选择的图片")
    }
    
    /// 测试错误处理和恢复流程
    func testErrorHandlingAndRecoveryFlow() async throws {
        // 1. 触发错误
        let invalidImage = UIImage() // 空图片
        mainViewModel.handleImageSelection(invalidImage)
        await colorExtractionViewModel.extractColors(from: invalidImage)
        
        // 2. 验证错误状态
        XCTAssertTrue(colorExtractionViewModel.extractionState.hasError, "应该处于错误状态")
        XCTAssertNotNil(colorExtractionViewModel.extractionState.errorMessage, "应该有错误消息")
        
        // 3. 尝试恢复
        guard let validImage = UIImage.createTestColorImage() else {
            XCTFail("无法创建有效的测试图片")
            return
        }
        
        mainViewModel.handleImageSelection(validImage)
        await colorExtractionViewModel.extractColors(from: validImage)
        
        // 4. 验证恢复成功
        XCTAssertFalse(colorExtractionViewModel.extractionState.hasError, "应该从错误状态恢复")
        XCTAssertTrue(colorExtractionViewModel.extractionState.hasResults, "应该有有效结果")
    }
    
    // MARK: - 服务集成测试
    
    /// 测试相机服务集成
    func testCameraServiceIntegration() throws {
        let cameraService = CameraService()
        
        // 测试相机可用性
        let isAvailable = cameraService.isCameraAvailable()
        
        if isAvailable {
            // 在真机上测试相机控制器创建
            XCTAssertNoThrow(try cameraService.createCameraController(), 
                           "相机可用时应该能创建控制器")
        } else {
            // 在模拟器上测试错误处理
            XCTAssertThrowsError(try cameraService.createCameraController(), 
                               "相机不可用时应该抛出错误")
        }
    }
    
    /// 测试相册服务集成
    func testPhotoLibraryServiceIntegration() {
        let photoLibraryService = PhotoLibraryService()
        
        // 测试相册可用性
        XCTAssertTrue(photoLibraryService.isPhotoLibraryAvailable(), 
                     "相册选择器应该总是可用")
        
        // 测试相册选择器创建
        XCTAssertNoThrow(photoLibraryService.createPhotoPickerController(), 
                        "应该能创建相册选择器")
    }
    
    /// 测试颜色提取服务集成
    func testColorExtractionServiceIntegration() async throws {
        let colorExtractionService = ColorExtractionService()
        
        guard let testImage = UIImage.createTestColorImage() else {
            XCTFail("无法创建测试图片")
            return
        }
        
        // 测试基本颜色提取
        let colors = try await colorExtractionService.extractDominantColors(from: testImage)
        XCTAssertFalse(colors.isEmpty, "应该提取到颜色")
        
        // 测试缓存功能
        let startTime = Date()
        let cachedColors = try await colorExtractionService.extractDominantColors(from: testImage)
        let duration = Date().timeIntervalSince(startTime)
        
        XCTAssertEqual(colors.count, cachedColors.count, "缓存的结果应该一致")
        XCTAssertLessThan(duration, 0.1, "缓存应该显著提升性能")
    }
    
    // MARK: - 导航集成测试
    
    /// 测试导航流程集成
    func testNavigationIntegration() {
        // 1. 初始导航状态
        XCTAssertEqual(mainViewModel.navigationPath.count, 0, "初始导航路径应该为空")
        
        // 2. 导航到颜色提取界面
        guard let testImage = UIImage.createTestColorImage() else {
            XCTFail("无法创建测试图片")
            return
        }
        
        mainViewModel.handleImageSelection(testImage)
        XCTAssertEqual(mainViewModel.navigationPath.count, 1, "应该导航到颜色提取界面")
        
        // 3. 返回主界面
        mainViewModel.navigateBack()
        XCTAssertEqual(mainViewModel.navigationPath.count, 0, "应该返回主界面")
    }
    
    // MARK: - 性能集成测试
    
    /// 测试端到端性能
    func testEndToEndPerformance() throws {
        guard let testImage = UIImage.createTestColorImage(size: CGSize(width: 500, height: 500)) else {
            XCTFail("无法创建测试图片")
            return
        }
        
        measure {
            let expectation = XCTestExpectation(description: "端到端流程完成")
            
            Task {
                // 模拟完整的用户流程
                self.mainViewModel.showImageSourceSheet()
                self.mainViewModel.handleImageSelection(testImage)
                await self.colorExtractionViewModel.extractColors(from: testImage)
                
                expectation.fulfill()
            }
            
            wait(for: [expectation], timeout: 10.0)
        }
    }
    
    /// 测试内存使用集成
    func testMemoryUsageIntegration() async throws {
        let initialMemory = getMemoryUsage()
        
        // 执行多次完整流程
        for i in 0..<5 {
            guard let testImage = UIImage.createTestColorImage() else {
                XCTFail("无法创建测试图片 \(i)")
                continue
            }
            
            mainViewModel.handleImageSelection(testImage)
            await colorExtractionViewModel.extractColors(from: testImage)
            colorExtractionViewModel.resetState()
        }
        
        // 强制垃圾回收
        autoreleasepool {}
        
        let finalMemory = getMemoryUsage()
        let memoryIncrease = finalMemory - initialMemory
        
        // 验证内存增长在合理范围内
        XCTAssertLessThan(memoryIncrease, 50 * 1024 * 1024, "内存增长不应超过50MB")
    }
    
    // MARK: - 辅助方法
    
    /// 获取当前内存使用量
    private func getMemoryUsage() -> UInt64 {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        return kerr == KERN_SUCCESS ? info.resident_size : 0
    }
}

// MARK: - 测试扩展

extension ColorExtractionState {
    /// 获取颜色结果（用于测试）
    var colors: [ExtractedColor] {
        switch self {
        case .success(let colors):
            return colors
        default:
            return []
        }
    }
}