//
//  ImageColorPickerPerformanceTests.swift
//  Color Palette XTests
//
//  Created by <PERSON><PERSON> on 2025/7/27.
//

import XCTest
@testable import Color_Palette_X

/// 图片取色功能性能测试
/// 
/// 专门测试颜色提取算法和相关功能的性能表现
final class ImageColorPickerPerformanceTests: XCTestCase {
    
    // MARK: - 测试属性
    
    var colorExtractionService: ColorExtractionService!
    
    // MARK: - 测试生命周期
    
    override func setUpWithError() throws {
        colorExtractionService = ColorExtractionService()
    }
    
    override func tearDownWithError() throws {
        colorExtractionService = nil
    }
    
    // MARK: - 颜色提取性能测试
    
    /// 测试小图片颜色提取性能
    func testSmallImageColorExtractionPerformance() throws {
        guard let testImage = UIImage.createTestColorImage(size: CGSize(width: 100, height: 100)) else {
            XCTFail("无法创建小尺寸测试图片")
            return
        }
        
        measure {
            let expectation = XCTestExpectation(description: "小图片颜色提取完成")
            
            Task {
                do {
                    _ = try await colorExtractionService.extractDominantColors(from: testImage)
                    expectation.fulfill()
                } catch {
                    XCTFail("小图片颜色提取失败: \(error)")
                    expectation.fulfill()
                }
            }
            
            wait(for: [expectation], timeout: 5.0)
        }
    }
    
    /// 测试中等图片颜色提取性能
    func testMediumImageColorExtractionPerformance() throws {
        guard let testImage = UIImage.createTestColorImage(size: CGSize(width: 500, height: 500)) else {
            XCTFail("无法创建中等尺寸测试图片")
            return
        }
        
        measure {
            let expectation = XCTestExpectation(description: "中等图片颜色提取完成")
            
            Task {
                do {
                    _ = try await colorExtractionService.extractDominantColors(from: testImage)
                    expectation.fulfill()
                } catch {
                    XCTFail("中等图片颜色提取失败: \(error)")
                    expectation.fulfill()
                }
            }
            
            wait(for: [expectation], timeout: 10.0)
        }
    }
    
    /// 测试大图片颜色提取性能
    func testLargeImageColorExtractionPerformance() throws {
        guard let testImage = UIImage.createTestColorImage(size: CGSize(width: 1000, height: 1000)) else {
            XCTFail("无法创建大尺寸测试图片")
            return
        }
        
        measure {
            let expectation = XCTestExpectation(description: "大图片颜色提取完成")
            
            Task {
                do {
                    _ = try await colorExtractionService.extractDominantColors(from: testImage)
                    expectation.fulfill()
                } catch {
                    XCTFail("大图片颜色提取失败: \(error)")
                    expectation.fulfill()
                }
            }
            
            wait(for: [expectation], timeout: 15.0)
        }
    }
    
    /// 测试不同颜色数量的性能影响
    func testColorCountPerformanceImpact() throws {
        guard let testImage = UIImage.createTestColorImage(size: CGSize(width: 300, height: 300)) else {
            XCTFail("无法创建测试图片")
            return
        }
        
        let colorCounts = [1, 4, 8, 16, 32]
        
        for count in colorCounts {
            measure(metrics: [XCTClockMetric()]) {
                let expectation = XCTestExpectation(description: "提取 \(count) 个颜色完成")
                
                Task {
                    do {
                        _ = try await colorExtractionService.extractDominantColors(from: testImage, count: count)
                        expectation.fulfill()
                    } catch {
                        XCTFail("提取 \(count) 个颜色失败: \(error)")
                        expectation.fulfill()
                    }
                }
                
                wait(for: [expectation], timeout: 10.0)
            }
        }
    }
    
    /// 测试缓存性能提升
    func testCachePerformanceImprovement() throws {
        guard let testImage = UIImage.createTestColorImage() else {
            XCTFail("无法创建测试图片")
            return
        }
        
        var firstRunTime: TimeInterval = 0
        var secondRunTime: TimeInterval = 0
        
        // 第一次运行（无缓存）
        measure(metrics: [XCTClockMetric()]) {
            let expectation = XCTestExpectation(description: "第一次颜色提取完成")
            let startTime = Date()
            
            Task {
                do {
                    _ = try await colorExtractionService.extractDominantColors(from: testImage)
                    firstRunTime = Date().timeIntervalSince(startTime)
                    expectation.fulfill()
                } catch {
                    XCTFail("第一次颜色提取失败: \(error)")
                    expectation.fulfill()
                }
            }
            
            wait(for: [expectation], timeout: 10.0)
        }
        
        // 第二次运行（使用缓存）
        measure(metrics: [XCTClockMetric()]) {
            let expectation = XCTestExpectation(description: "第二次颜色提取完成")
            let startTime = Date()
            
            Task {
                do {
                    _ = try await colorExtractionService.extractDominantColors(from: testImage)
                    secondRunTime = Date().timeIntervalSince(startTime)
                    expectation.fulfill()
                } catch {
                    XCTFail("第二次颜色提取失败: \(error)")
                    expectation.fulfill()
                }
            }
            
            wait(for: [expectation], timeout: 10.0)
        }
        
        // 验证缓存提升了性能
        XCTAssertLessThan(secondRunTime, firstRunTime, "缓存应该提升性能")
    }
    
    // MARK: - 并发性能测试
    
    /// 测试并发颜色提取性能
    func testConcurrentColorExtractionPerformance() throws {
        guard let testImage = UIImage.createTestColorImage() else {
            XCTFail("无法创建测试图片")
            return
        }
        
        measure {
            let expectation = XCTestExpectation(description: "并发颜色提取完成")
            expectation.expectedFulfillmentCount = 5
            
            // 启动5个并发任务
            for i in 0..<5 {
                Task {
                    do {
                        _ = try await colorExtractionService.extractDominantColors(from: testImage)
                        expectation.fulfill()
                    } catch {
                        XCTFail("并发任务 \(i) 失败: \(error)")
                        expectation.fulfill()
                    }
                }
            }
            
            wait(for: [expectation], timeout: 15.0)
        }
    }
    
    /// 测试取消操作的性能影响
    func testCancellationPerformance() throws {
        guard let testImage = UIImage.createTestColorImage(size: CGSize(width: 800, height: 800)) else {
            XCTFail("无法创建大尺寸测试图片")
            return
        }
        
        measure {
            let expectation = XCTestExpectation(description: "取消操作完成")
            
            Task {
                // 开始颜色提取
                let extractionTask = Task {
                    try await colorExtractionService.extractDominantColors(from: testImage)
                }
                
                // 短暂延迟后取消
                try await Task.sleep(nanoseconds: 100_000_000) // 0.1秒
                colorExtractionService.cancelCurrentTask()
                
                do {
                    _ = try await extractionTask.value
                } catch {
                    // 取消操作是预期的
                }
                
                expectation.fulfill()
            }
            
            wait(for: [expectation], timeout: 5.0)
        }
    }
    
    // MARK: - 内存性能测试
    
    /// 测试内存使用效率
    func testMemoryUsageEfficiency() throws {
        let initialMemory = getMemoryUsage()
        
        // 处理多张图片
        let imageSizes = [
            CGSize(width: 100, height: 100),
            CGSize(width: 300, height: 300),
            CGSize(width: 500, height: 500)
        ]
        
        measure(metrics: [XCTMemoryMetric()]) {
            let expectation = XCTestExpectation(description: "内存测试完成")
            expectation.expectedFulfillmentCount = imageSizes.count
            
            for size in imageSizes {
                Task {
                    guard let testImage = UIImage.createTestColorImage(size: size) else {
                        expectation.fulfill()
                        return
                    }
                    
                    do {
                        _ = try await colorExtractionService.extractDominantColors(from: testImage)
                        expectation.fulfill()
                    } catch {
                        XCTFail("处理尺寸 \(size) 的图片失败: \(error)")
                        expectation.fulfill()
                    }
                }
            }
            
            wait(for: [expectation], timeout: 20.0)
        }
        
        // 强制垃圾回收
        autoreleasepool {}
        
        let finalMemory = getMemoryUsage()
        let memoryIncrease = finalMemory - initialMemory
        
        // 验证内存增长在合理范围内
        XCTAssertLessThan(memoryIncrease, 100 * 1024 * 1024, "内存增长不应超过100MB")
    }
    
    /// 测试内存泄漏
    func testMemoryLeaks() throws {
        weak var weakService: ColorExtractionService?
        
        autoreleasepool {
            let service = ColorExtractionService()
            weakService = service
            
            guard let testImage = UIImage.createTestColorImage() else {
                XCTFail("无法创建测试图片")
                return
            }
            
            let expectation = XCTestExpectation(description: "颜色提取完成")
            
            Task {
                do {
                    _ = try await service.extractDominantColors(from: testImage)
                    expectation.fulfill()
                } catch {
                    XCTFail("颜色提取失败: \(error)")
                    expectation.fulfill()
                }
            }
            
            wait(for: [expectation], timeout: 10.0)
        }
        
        // 验证服务对象被正确释放
        XCTAssertNil(weakService, "ColorExtractionService 应该被正确释放")
    }
    
    // MARK: - 算法性能测试
    
    /// 测试不同算法参数的性能影响
    func testAlgorithmParametersPerformance() throws {
        guard let testImage = UIImage.createTestColorImage(size: CGSize(width: 400, height: 400)) else {
            XCTFail("无法创建测试图片")
            return
        }
        
        let passesValues = [1, 3, 5, 10]
        
        for passes in passesValues {
            measure(metrics: [XCTClockMetric()]) {
                let expectation = XCTestExpectation(description: "算法参数测试完成")
                
                Task {
                    do {
                        _ = try await testImage.extractDominantColors(count: 8, passes: passes)
                        expectation.fulfill()
                    } catch {
                        XCTFail("算法参数 passes=\(passes) 测试失败: \(error)")
                        expectation.fulfill()
                    }
                }
                
                wait(for: [expectation], timeout: 15.0)
            }
        }
    }
    
    /// 测试感知色彩空间的性能影响
    func testPerceptualColorSpacePerformance() throws {
        guard let testImage = UIImage.createTestColorImage() else {
            XCTFail("无法创建测试图片")
            return
        }
        
        // 测试非感知色彩空间
        measure(metrics: [XCTClockMetric()]) {
            let expectation = XCTestExpectation(description: "非感知色彩空间测试完成")
            
            Task {
                do {
                    _ = try await testImage.extractDominantColors(perceptual: false)
                    expectation.fulfill()
                } catch {
                    XCTFail("非感知色彩空间测试失败: \(error)")
                    expectation.fulfill()
                }
            }
            
            wait(for: [expectation], timeout: 10.0)
        }
        
        // 测试感知色彩空间
        measure(metrics: [XCTClockMetric()]) {
            let expectation = XCTestExpectation(description: "感知色彩空间测试完成")
            
            Task {
                do {
                    _ = try await testImage.extractDominantColors(perceptual: true)
                    expectation.fulfill()
                } catch {
                    XCTFail("感知色彩空间测试失败: \(error)")
                    expectation.fulfill()
                }
            }
            
            wait(for: [expectation], timeout: 10.0)
        }
    }
    
    // MARK: - 辅助方法
    
    /// 获取当前内存使用量
    private func getMemoryUsage() -> UInt64 {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        return kerr == KERN_SUCCESS ? info.resident_size : 0
    }
}

// MARK: - 性能基准测试

extension ImageColorPickerPerformanceTests {
    
    /// 建立性能基准
    func testPerformanceBaseline() throws {
        guard let testImage = UIImage.createTestColorImage(size: CGSize(width: 300, height: 300)) else {
            XCTFail("无法创建基准测试图片")
            return
        }
        
        // 这个测试用于建立性能基准，可以在不同设备上比较
        let options = XCTMeasureOptions()
        options.iterationCount = 10
        
        measure(options: options) {
            let expectation = XCTestExpectation(description: "基准测试完成")
            
            Task {
                do {
                    let startTime = Date()
                    _ = try await colorExtractionService.extractDominantColors(from: testImage)
                    let duration = Date().timeIntervalSince(startTime)
                    
                    AppLogger.info("基准测试耗时: \(duration)秒", category: .performance)
                    expectation.fulfill()
                } catch {
                    XCTFail("基准测试失败: \(error)")
                    expectation.fulfill()
                }
            }
            
            wait(for: [expectation], timeout: 10.0)
        }
    }
}