//
//  Color_Palette_XTests.swift
//  Color Palette XTests
//
//  Created by <PERSON><PERSON><PERSON> on 2025/7/12.
//

import XCTest
import SwiftUI
@testable import Color_Palette_X

final class Color_Palette_XTests: XCTestCase {

    override func setUpWithError() throws {
        // Put setup code here. This method is called before the invocation of each test method in the class.
    }

    override func tearDownWithError() throws {
        // Put teardown code here. This method is called after the invocation of each test method in the class.
    }

    func testContentViewInitialization() throws {
        // 测试 ContentView 能够正确初始化
        let contentView = ContentView()
        XCTAssertNotNil(contentView)
    }

    func testHomeViewInitialization() throws {
        // 测试 HomeView 能够正确初始化
        let homeView = HomeView()
        XCTAssertNotNil(homeView)
    }

    func testToolCardInitialization() throws {
        // 测试 ToolCard 能够正确初始化
        let toolCard = ToolCard(
            icon: "paintbrush.fill",
            title: LocalizedStringKey("gradient_presets"),
            color: .indigo
        )
        XCTAssertNotNil(toolCard)
    }

    func testFavoritesViewInitialization() throws {
        // 测试 FavoritesView 能够正确初始化
        let favoritesView = FavoritesView()
        XCTAssertNotNil(favoritesView)
    }

    func testProfileViewInitialization() throws {
        // 测试 ProfileView 能够正确初始化
        let profileView = ProfileView()
        XCTAssertNotNil(profileView)
    }

    func testWelcomeViewInitialization() throws {
        // 测试 WelcomeView 能够正确初始化
        let welcomeView = WelcomeView()
        XCTAssertNotNil(welcomeView)
    }

    func testFeatureCardInitialization() throws {
        // 测试 FeatureCard 能够正确初始化
        let featureCard = FeatureCard(
            icon: "paintbrush.fill",
            title: LocalizedStringKey("gradient_creation"),
            description: LocalizedStringKey("gradient_creation_desc"),
            color: .indigo
        )
        XCTAssertNotNil(featureCard)
    }

    func testPerformanceExample() throws {
        // This is an example of a performance test case.
        self.measure {
            // Put the code you want to measure the time of here.
        }
    }

}
